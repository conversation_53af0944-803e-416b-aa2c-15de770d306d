/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static generation with ISR support
  trailingSlash: true,

  // Image optimization
  images: {
    domains: [],
    formats: ['image/webp', 'image/avif'],
  },

  // Enable experimental features for better SEO
  experimental: {
    // optimizeCss: true, // Disabled due to critters module issue
  },

  // Compress output
  compress: true,

  // Generate unique build ID
  generateBuildId: async () => {
    return 'bible-quiz-build-' + Date.now()
  },

  // Optimize for SEO
  poweredByHeader: false,

  // Headers for better SEO and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ]
  },
};

export default nextConfig;
