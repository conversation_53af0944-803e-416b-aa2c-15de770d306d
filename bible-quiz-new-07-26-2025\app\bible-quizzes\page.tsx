'use client';

import { useState } from 'react';
import Link from 'next/link';
import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';
import { bibleBooks } from '../../data/bible-books';
import { bibleCharacters } from '../../data/bible-characters';
import { quizThemes } from '../../data/quiz-themes';

interface QuizCard {
  id: string;
  title: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
  type: 'chapter' | 'book' | 'character' | 'theme';
  testament?: 'old' | 'new';
  questionCount: number;
  estimatedTime: number;
  completedCount: number;
  href: string;
  image?: string;
}

// Generate sample quiz cards from our data
const generateQuizCards = (): QuizCard[] => {
  const cards: QuizCard[] = [];
  
  // Featured book quizzes
  const featuredBooks = ['genesis', 'matthew', 'john', 'romans', 'psalms', 'revelation'];
  featuredBooks.forEach(bookSlug => {
    const book = bibleBooks.find(b => b.slug === bookSlug);
    if (book) {
      cards.push({
        id: `${book.slug}-quiz`,
        title: `${book.name} Quiz`,
        description: `Comprehensive quiz covering the entire book of ${book.name}`,
        difficulty: 'medium',
        type: 'book',
        testament: book.testament,
        questionCount: Math.floor(book.chapters * 2.5),
        estimatedTime: Math.ceil(book.chapters / 3),
        completedCount: Math.floor(Math.random() * 5000) + 1000,
        href: `/${book.slug}-quiz`,
      });
    }
  });
  
  // Featured character quizzes
  const featuredCharacters = ['jesus', 'moses', 'david', 'paul', 'abraham', 'mary'];
  featuredCharacters.forEach(characterSlug => {
    const character = bibleCharacters.find(c => c.slug === characterSlug);
    if (character) {
      cards.push({
        id: `${character.slug}-quiz`,
        title: `${character.name} Quiz`,
        description: `Test your knowledge about ${character.name}, ${character.keyRole.toLowerCase()}`,
        difficulty: 'medium',
        type: 'character',
        testament: character.testament === 'both' ? 'old' : character.testament,
        questionCount: 20,
        estimatedTime: 8,
        completedCount: Math.floor(Math.random() * 3000) + 500,
        href: `/${character.slug}-quiz`,
      });
    }
  });
  
  // Featured theme quizzes
  quizThemes.slice(0, 6).forEach(theme => {
    cards.push({
      id: theme.slug,
      title: theme.name,
      description: theme.description,
      difficulty: 'medium',
      type: 'theme',
      questionCount: 15,
      estimatedTime: 10,
      completedCount: Math.floor(Math.random() * 2000) + 300,
      href: `/${theme.slug}`,
    });
  });
  
  // Sample chapter quizzes
  const sampleChapters = [
    { book: 'genesis', chapter: 1 },
    { book: 'john', chapter: 3 },
    { book: 'romans', chapter: 8 },
    { book: 'psalms', chapter: 23 },
  ];
  
  sampleChapters.forEach(({ book: bookSlug, chapter }) => {
    const book = bibleBooks.find(b => b.slug === bookSlug);
    if (book) {
      cards.push({
        id: `${book.slug}-${chapter}-quiz`,
        title: `${book.name} ${chapter} Quiz`,
        description: `Test your knowledge of ${book.name} chapter ${chapter}`,
        difficulty: 'easy',
        type: 'chapter',
        testament: book.testament,
        questionCount: 10,
        estimatedTime: 5,
        completedCount: Math.floor(Math.random() * 1500) + 200,
        href: `/${book.slug}-${chapter}-quiz`,
      });
    }
  });
  
  return cards;
};

export default function BibleQuizzesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTestament, setSelectedTestament] = useState<'all' | 'old' | 'new'>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<'all' | 'easy' | 'medium' | 'hard'>('all');
  const [selectedType, setSelectedType] = useState<'all' | 'chapter' | 'book' | 'character' | 'theme'>('all');
  const [showFilters, setShowFilters] = useState(false);
  
  const allQuizzes = generateQuizCards();
  
  const filteredQuizzes = allQuizzes.filter(quiz => {
    const matchesSearch = quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quiz.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTestament = selectedTestament === 'all' || quiz.testament === selectedTestament;
    const matchesDifficulty = selectedDifficulty === 'all' || quiz.difficulty === selectedDifficulty;
    const matchesType = selectedType === 'all' || quiz.type === selectedType;
    
    return matchesSearch && matchesTestament && matchesDifficulty && matchesType;
  });

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl font-bold mb-4">Bible Quizzes</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-8">
              Explore our comprehensive collection of Bible quizzes covering all 66 books, 
              major characters, and important themes. Test your knowledge and deepen your understanding of Scripture.
            </p>
            <div className="flex justify-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold">1,500+</div>
                  <div className="text-blue-200">Total Quizzes</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">66</div>
                  <div className="text-blue-200">Bible Books</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">200+</div>
                  <div className="text-blue-200">Characters</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">50+</div>
                  <div className="text-blue-200">Themes</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="py-8 bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search quizzes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FunnelIcon className="h-5 w-5" />
              Filters
            </button>
          </div>
          
          {/* Filter Options */}
          {showFilters && (
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Testament</label>
                <select
                  value={selectedTestament}
                  onChange={(e) => setSelectedTestament(e.target.value as 'all' | 'old' | 'new')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Testaments</option>
                  <option value="old">Old Testament</option>
                  <option value="new">New Testament</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
                <select
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value as 'all' | 'easy' | 'medium' | 'hard')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Difficulties</option>
                  <option value="easy">Easy</option>
                  <option value="medium">Medium</option>
                  <option value="hard">Hard</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value as 'all' | 'chapter' | 'book' | 'character' | 'theme')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Types</option>
                  <option value="chapter">Chapter Quizzes</option>
                  <option value="book">Book Quizzes</option>
                  <option value="character">Character Quizzes</option>
                  <option value="theme">Theme Quizzes</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Quiz Grid */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900">
              {filteredQuizzes.length} Quiz{filteredQuizzes.length !== 1 ? 'es' : ''} Found
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredQuizzes.map((quiz) => (
              <div key={quiz.id} className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow overflow-hidden group">
                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      quiz.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
                      quiz.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {quiz.difficulty.charAt(0).toUpperCase() + quiz.difficulty.slice(1)}
                    </span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      {quiz.type.charAt(0).toUpperCase() + quiz.type.slice(1)}
                    </span>
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                    {quiz.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">{quiz.description}</p>
                  
                  <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                    <span>{quiz.questionCount} questions</span>
                    <span>{quiz.estimatedTime} min</span>
                  </div>
                  
                  <div className="text-xs text-gray-400 mb-4">
                    {quiz.completedCount.toLocaleString()} people completed
                  </div>
                  
                  <Link
                    href={quiz.href}
                    className="block w-full bg-blue-600 text-white text-center py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  >
                    Take Quiz
                  </Link>
                </div>
              </div>
            ))}
          </div>
          
          {filteredQuizzes.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 text-lg mb-4">No quizzes found matching your criteria</div>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedTestament('all');
                  setSelectedDifficulty('all');
                  setSelectedType('all');
                }}
                className="text-blue-600 hover:text-blue-700 font-medium"
              >
                Clear all filters
              </button>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
