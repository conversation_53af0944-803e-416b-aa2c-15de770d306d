import { MetadataRoute } from 'next';
import { generateAllQuizSlugs } from '../lib/quiz-generator';
import { bibleBooks } from '../data/bible-books';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://salvationcall.com';
  
  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/bible-quizzes`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
  ];

  // Generate all quiz pages
  const quizSlugs = generateAllQuizSlugs();
  const quizPages = quizSlugs.map(slug => ({
    url: `${baseUrl}/${slug}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.8,
  }));

  // Bible book category pages (future enhancement)
  const bookPages = bibleBooks.map(book => ({
    url: `${baseUrl}/bible-quizzes/${book.name.toLowerCase().replace(/\s+/g, '-')}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.7,
  }));

  return [...staticPages, ...quizPages, ...bookPages];
}
