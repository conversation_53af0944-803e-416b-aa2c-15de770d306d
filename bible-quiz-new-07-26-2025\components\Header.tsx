'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronDownIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

const navigation = [
  {
    name: 'Bible Quizzes',
    href: '/bible-quizzes',
    children: [
      { name: 'All Bible Quizzes', href: '/bible-quizzes/' },
      { name: 'Old Testament', href: '/old-testament-quizzes/' },
      { name: 'New Testament', href: '/new-testament-quizzes/' },
      { name: 'By Difficulty', href: '/bible-quiz-difficulty/' },
      { name: 'Kids & Youth', href: '/kids-bible-quiz/' },
      { name: 'Popular Quizzes', href: '/popular-bible-quizzes/' },
      { name: 'Daily Quiz', href: '/daily-bible-quiz/' },
    ],
  },
  {
    name: 'Study Guides',
    href: '/study-guides',
    children: [
      { name: 'All Study Guides', href: '/bible-study-guides/' },
      { name: 'Book Studies', href: '/bible-book-studies/' },
      { name: 'Chapter Studies', href: '/bible-chapter-studies/' },
      { name: 'Reading Plans', href: '/bible-reading-plans/' },
      { name: 'Discussion Questions', href: '/bible-discussion-questions/' },
    ],
  },
  {
    name: 'Characters',
    href: '/characters',
    children: [
      { name: 'All Characters', href: '/bible-characters/' },
      { name: 'Old Testament', href: '/old-testament-characters/' },
      { name: 'New Testament', href: '/new-testament-characters/' },
      { name: 'Women in Bible', href: '/women-bible-characters/' },
      { name: 'Character Studies', href: '/bible-character-studies/' },
    ],
  },
  {
    name: 'Resources',
    href: '/resources',
    children: [
      { name: 'How to Study', href: '/how-to-study-bible/' },
      { name: 'Bible Timeline', href: '/bible-timeline/' },
      { name: 'Memory Verses', href: '/bible-memory-verses/' },
      { name: 'Downloads', href: '/bible-study-downloads/' },
      { name: 'Teaching Materials', href: '/bible-teaching-materials/' },
    ],
  },
  { name: 'About', href: '/about' },
];

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8" aria-label="Top">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">BC</span>
              </div>
              <span className="text-xl font-bold text-gray-900">SalvationCall</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex lg:items-center lg:space-x-8">
            <Link
              href="/"
              className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
            >
              Home
            </Link>
            
            {navigation.map((item) => (
              <div key={item.name} className="relative group">
                <button className="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                  {item.name}
                  {item.children && (
                    <ChevronDownIcon className="ml-1 h-4 w-4 group-hover:rotate-180 transition-transform" />
                  )}
                </button>
                
                {item.children && (
                  <div className="absolute left-0 mt-2 w-56 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="py-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
            
            <Link
              href="/login"
              className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              Login
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              type="button"
              className="text-gray-700 hover:text-blue-600 p-2"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? (
                <XMarkIcon className="h-6 w-6" />
              ) : (
                <Bars3Icon className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="lg:hidden border-t border-gray-200 pt-4 pb-4">
            <div className="space-y-1">
              <Link
                href="/"
                className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                🏠 Home
              </Link>
              
              {navigation.map((item) => (
                <div key={item.name}>
                  <div className="px-3 py-2 text-base font-medium text-gray-900 border-b border-gray-100">
                    📖 {item.name}
                  </div>
                  {item.children && (
                    <div className="pl-6 space-y-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          className="block px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              
              <Link
                href="/login"
                className="block px-3 py-2 text-base font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors mt-4"
                onClick={() => setMobileMenuOpen(false)}
              >
                👤 Login
              </Link>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}
