import { Quiz, QuizQuestion } from '../types/bible';

// Genesis Chapter 1 Quiz - Creation
const genesis1Questions: QuizQuestion[] = [
  {
    id: 'gen1-q1',
    question: 'In the beginning God created what?',
    options: ['The earth only', 'The heavens and the earth', 'Light and darkness', 'Man and woman'],
    correctAnswer: 1,
    explanation: 'Genesis 1:1 states "In the beginning God created the heavens and the earth."',
    difficulty: 'easy',
    reference: 'Genesis 1:1',
    category: 'creation'
  },
  {
    id: 'gen1-q2',
    question: 'What was the earth like before God began creating?',
    options: ['Beautiful and perfect', 'Without form and void', 'Full of animals', 'Covered with plants'],
    correctAnswer: 1,
    explanation: 'The earth was without form and void, and darkness was over the face of the deep.',
    difficulty: 'easy',
    reference: 'Genesis 1:2',
    category: 'creation'
  },
  {
    id: 'gen1-q3',
    question: 'What was God\'s first creative act?',
    options: ['Creating animals', 'Creating light', 'Creating water', 'Creating land'],
    correctAnswer: 1,
    explanation: 'God said "Let there be light," and there was light.',
    difficulty: 'easy',
    reference: 'Genesis 1:3',
    category: 'creation'
  },
  {
    id: 'gen1-q4',
    question: 'What did God call the light?',
    options: ['Sun', 'Day', 'Morning', 'Brightness'],
    correctAnswer: 1,
    explanation: 'God called the light Day, and the darkness he called Night.',
    difficulty: 'easy',
    reference: 'Genesis 1:5',
    category: 'creation'
  },
  {
    id: 'gen1-q5',
    question: 'What did God create on the second day?',
    options: ['Land and seas', 'The firmament (sky)', 'Plants and trees', 'Sun and moon'],
    correctAnswer: 1,
    explanation: 'God made the firmament and divided the waters above from the waters below.',
    difficulty: 'easy',
    reference: 'Genesis 1:6-8',
    category: 'creation'
  },
  {
    id: 'gen1-q6',
    question: 'What did God call the firmament?',
    options: ['Sky', 'Heaven', 'Atmosphere', 'Space'],
    correctAnswer: 1,
    explanation: 'And God called the firmament Heaven.',
    difficulty: 'easy',
    reference: 'Genesis 1:8',
    category: 'creation'
  },
  {
    id: 'gen1-q7',
    question: 'On the third day, what appeared when the waters were gathered together?',
    options: ['Mountains', 'Dry land', 'Rivers', 'Islands'],
    correctAnswer: 1,
    explanation: 'God gathered the waters and the dry land appeared, which He called Earth.',
    difficulty: 'easy',
    reference: 'Genesis 1:9-10',
    category: 'creation'
  },
  {
    id: 'gen1-q8',
    question: 'What else did God create on the third day besides dry land?',
    options: ['Animals', 'Fish', 'Plants and trees', 'Birds'],
    correctAnswer: 2,
    explanation: 'God created grass, herbs, and fruit trees on the third day.',
    difficulty: 'easy',
    reference: 'Genesis 1:11-13',
    category: 'creation'
  },
  {
    id: 'gen1-q9',
    question: 'What did God create on the fourth day?',
    options: ['Animals', 'Sun, moon, and stars', 'Fish and birds', 'Man'],
    correctAnswer: 1,
    explanation: 'God created the sun, moon, and stars to give light and mark seasons.',
    difficulty: 'easy',
    reference: 'Genesis 1:14-19',
    category: 'creation'
  },
  {
    id: 'gen1-q10',
    question: 'What was the purpose of the lights in the firmament?',
    options: ['To give light only', 'To divide day and night, and for signs and seasons', 'To create heat', 'To make the earth beautiful'],
    correctAnswer: 1,
    explanation: 'The lights were for signs, seasons, days, and years, and to give light.',
    difficulty: 'medium',
    reference: 'Genesis 1:14',
    category: 'creation'
  },
  {
    id: 'gen1-q11',
    question: 'What did God create on the fifth day?',
    options: ['Land animals', 'Fish and birds', 'Plants', 'Man'],
    correctAnswer: 1,
    explanation: 'God created fish and every winged bird on the fifth day.',
    difficulty: 'easy',
    reference: 'Genesis 1:20-23',
    category: 'creation'
  },
  {
    id: 'gen1-q12',
    question: 'What was God\'s first blessing recorded in Genesis?',
    options: ['Blessing the light', 'Blessing the land', 'Blessing the fish and birds to multiply', 'Blessing the plants'],
    correctAnswer: 2,
    explanation: 'God blessed the fish and birds, saying "Be fruitful and multiply."',
    difficulty: 'medium',
    reference: 'Genesis 1:22',
    category: 'creation'
  },
  {
    id: 'gen1-q13',
    question: 'What did God create first on the sixth day?',
    options: ['Man', 'Land animals', 'Woman', 'The garden'],
    correctAnswer: 1,
    explanation: 'God created cattle, creeping things, and beasts of the earth first on the sixth day.',
    difficulty: 'medium',
    reference: 'Genesis 1:24-25',
    category: 'creation'
  },
  {
    id: 'gen1-q14',
    question: 'In whose image did God create man?',
    options: ['Angels\' image', 'Animals\' image', 'His own image', 'No specific image'],
    correctAnswer: 2,
    explanation: 'God created man in His own image, in the image of God He created him.',
    difficulty: 'easy',
    reference: 'Genesis 1:27',
    category: 'creation'
  },
  {
    id: 'gen1-q15',
    question: 'What dominion did God give to man?',
    options: ['Over the earth only', 'Over fish, birds, and every living thing', 'Over plants only', 'Over the heavens'],
    correctAnswer: 1,
    explanation: 'God gave man dominion over fish, birds, cattle, and every creeping thing.',
    difficulty: 'medium',
    reference: 'Genesis 1:28',
    category: 'creation'
  },
  {
    id: 'gen1-q16',
    question: 'What did God give man and animals for food?',
    options: ['Meat', 'Every green herb and fruit', 'Fish', 'Bread'],
    correctAnswer: 1,
    explanation: 'God gave every herb and fruit tree for food to man and animals.',
    difficulty: 'medium',
    reference: 'Genesis 1:29-30',
    category: 'creation'
  },
  {
    id: 'gen1-q17',
    question: 'How did God view His completed creation?',
    options: ['It was good', 'It was very good', 'It was perfect', 'It was acceptable'],
    correctAnswer: 1,
    explanation: 'God saw everything that He had made, and indeed it was very good.',
    difficulty: 'easy',
    reference: 'Genesis 1:31',
    category: 'creation'
  },
  {
    id: 'gen1-q18',
    question: 'What did God do on the seventh day?',
    options: ['Continued creating', 'Rested from His work', 'Created the garden', 'Made woman'],
    correctAnswer: 1,
    explanation: 'God rested on the seventh day from all His work which He had done.',
    difficulty: 'easy',
    reference: 'Genesis 2:2',
    category: 'creation'
  }
];

// Genesis Chapter 2 Quiz - Garden of Eden
const genesis2Questions: QuizQuestion[] = [
  {
    id: 'gen2-q1',
    question: 'What did God do to the seventh day?',
    options: ['Made it holy', 'Blessed and sanctified it', 'Called it good', 'Made it the first day'],
    correctAnswer: 1,
    explanation: 'God blessed the seventh day and sanctified it, because in it He rested.',
    difficulty: 'easy',
    reference: 'Genesis 2:3',
    category: 'sabbath'
  },
  {
    id: 'gen2-q2',
    question: 'What was missing from the earth before God sent rain?',
    options: ['Animals', 'A man to till the ground', 'Plants', 'Light'],
    correctAnswer: 1,
    explanation: 'There was no man to till the ground, so no rain had fallen.',
    difficulty: 'medium',
    reference: 'Genesis 2:5',
    category: 'creation'
  },
  {
    id: 'gen2-q3',
    question: 'How did God form man?',
    options: ['From nothing', 'From the dust of the ground', 'From water', 'From light'],
    correctAnswer: 1,
    explanation: 'The Lord God formed man of the dust of the ground.',
    difficulty: 'easy',
    reference: 'Genesis 2:7',
    category: 'creation'
  },
  {
    id: 'gen2-q4',
    question: 'How did man become a living soul?',
    options: ['God spoke to him', 'God breathed into his nostrils the breath of life', 'God touched him', 'God gave him a heart'],
    correctAnswer: 1,
    explanation: 'God breathed into his nostrils the breath of life; and man became a living soul.',
    difficulty: 'easy',
    reference: 'Genesis 2:7',
    category: 'creation'
  },
  {
    id: 'gen2-q5',
    question: 'Where did God plant a garden?',
    options: ['In the west', 'In Eden, eastward', 'In the north', 'In the center of the earth'],
    correctAnswer: 1,
    explanation: 'The Lord God planted a garden eastward in Eden.',
    difficulty: 'easy',
    reference: 'Genesis 2:8',
    category: 'eden'
  },
  {
    id: 'gen2-q6',
    question: 'What did God put in the midst of the garden?',
    options: ['A fountain', 'The tree of life and the tree of knowledge of good and evil', 'A house', 'An altar'],
    correctAnswer: 1,
    explanation: 'God put the tree of life and the tree of the knowledge of good and evil in the midst of the garden.',
    difficulty: 'medium',
    reference: 'Genesis 2:9',
    category: 'eden'
  },
  {
    id: 'gen2-q7',
    question: 'What was man\'s job in the garden?',
    options: ['To rest', 'To tend and keep it', 'To eat from every tree', 'To name the animals only'],
    correctAnswer: 1,
    explanation: 'The Lord God took the man and put him in the garden to tend and keep it.',
    difficulty: 'easy',
    reference: 'Genesis 2:15',
    category: 'eden'
  },
  {
    id: 'gen2-q8',
    question: 'What command did God give man about the trees?',
    options: ['Eat from every tree', 'Don\'t eat from the tree of knowledge of good and evil', 'Don\'t eat from any tree', 'Only eat fruit'],
    correctAnswer: 1,
    explanation: 'God said man could eat from every tree except the tree of the knowledge of good and evil.',
    difficulty: 'easy',
    reference: 'Genesis 2:16-17',
    category: 'commandment'
  },
  {
    id: 'gen2-q9',
    question: 'What would happen if man ate from the forbidden tree?',
    options: ['He would become wise', 'He would surely die', 'He would become like God', 'Nothing would happen'],
    correctAnswer: 1,
    explanation: 'God said "in the day that you eat of it you shall surely die."',
    difficulty: 'easy',
    reference: 'Genesis 2:17',
    category: 'commandment'
  },
  {
    id: 'gen2-q10',
    question: 'What did God say was not good?',
    options: ['The garden', 'That man should be alone', 'The animals', 'The trees'],
    correctAnswer: 1,
    explanation: 'The Lord God said, "It is not good that man should be alone."',
    difficulty: 'easy',
    reference: 'Genesis 2:18',
    category: 'relationship'
  },
  {
    id: 'gen2-q11',
    question: 'What did God decide to make for man?',
    options: ['More food', 'A helper comparable to him', 'A house', 'More animals'],
    correctAnswer: 1,
    explanation: 'God said "I will make him a helper comparable to him."',
    difficulty: 'easy',
    reference: 'Genesis 2:18',
    category: 'relationship'
  },
  {
    id: 'gen2-q12',
    question: 'Who named all the animals?',
    options: ['God', 'Adam', 'The angels', 'No one'],
    correctAnswer: 1,
    explanation: 'Adam gave names to all cattle, birds, and beasts of the field.',
    difficulty: 'easy',
    reference: 'Genesis 2:20',
    category: 'naming'
  },
  {
    id: 'gen2-q13',
    question: 'How did God create woman?',
    options: ['From dust', 'From a rib taken from man', 'From nothing', 'From water'],
    correctAnswer: 1,
    explanation: 'God caused a deep sleep to fall on Adam and made woman from his rib.',
    difficulty: 'easy',
    reference: 'Genesis 2:21-22',
    category: 'creation'
  },
  {
    id: 'gen2-q14',
    question: 'What did Adam say when he saw the woman?',
    options: ['"This is good"', '"This is now bone of my bones and flesh of my flesh"', '"She is beautiful"', '"Thank you God"'],
    correctAnswer: 1,
    explanation: 'Adam said "This is now bone of my bones and flesh of my flesh; she shall be called Woman."',
    difficulty: 'medium',
    reference: 'Genesis 2:23',
    category: 'relationship'
  },
  {
    id: 'gen2-q15',
    question: 'Why is woman called Woman?',
    options: ['Because she was wise', 'Because she was taken out of Man', 'Because she was beautiful', 'Because she was first'],
    correctAnswer: 1,
    explanation: 'She shall be called Woman, because she was taken out of Man.',
    difficulty: 'medium',
    reference: 'Genesis 2:23',
    category: 'relationship'
  },
  {
    id: 'gen2-q16',
    question: 'What should a man do according to Genesis 2:24?',
    options: ['Stay with his parents', 'Leave his father and mother and be joined to his wife', 'Live alone', 'Work hard'],
    correctAnswer: 1,
    explanation: 'A man shall leave his father and mother and be joined to his wife, and they shall become one flesh.',
    difficulty: 'medium',
    reference: 'Genesis 2:24',
    category: 'marriage'
  },
  {
    id: 'gen2-q17',
    question: 'How were Adam and Eve before sin entered?',
    options: ['Clothed', 'Naked and not ashamed', 'Afraid', 'Hiding'],
    correctAnswer: 1,
    explanation: 'They were both naked, the man and his wife, and were not ashamed.',
    difficulty: 'easy',
    reference: 'Genesis 2:25',
    category: 'innocence'
  }
];

// Genesis Book Quiz - Comprehensive 25 questions covering the entire book
const genesisBookQuestions: QuizQuestion[] = [
  {
    id: 'genbook-q1',
    question: 'Who are the three main patriarchs featured in Genesis?',
    options: ['Adam, Noah, Moses', 'Abraham, Isaac, Jacob', 'Cain, Abel, Seth', 'Noah, Abraham, Joseph'],
    correctAnswer: 1,
    explanation: 'Abraham, Isaac, and Jacob are the three main patriarchs whose stories dominate Genesis.',
    difficulty: 'easy',
    reference: 'Genesis 12-50',
    category: 'patriarchs'
  },
  {
    id: 'genbook-q2',
    question: 'What was God\'s covenant sign with Noah?',
    options: ['A dove', 'The rainbow', 'An olive branch', 'The ark'],
    correctAnswer: 1,
    explanation: 'God set His rainbow in the cloud as a sign of His covenant with Noah.',
    difficulty: 'easy',
    reference: 'Genesis 9:13',
    category: 'covenant'
  },
  {
    id: 'genbook-q3',
    question: 'What was Abraham\'s original name?',
    options: ['Abram', 'Abner', 'Abel', 'Aaron'],
    correctAnswer: 0,
    explanation: 'God changed Abram\'s name to Abraham when He made His covenant with him.',
    difficulty: 'easy',
    reference: 'Genesis 17:5',
    category: 'patriarchs'
  },
  {
    id: 'genbook-q4',
    question: 'Which son did Abraham almost sacrifice?',
    options: ['Ishmael', 'Isaac', 'Jacob', 'Esau'],
    correctAnswer: 1,
    explanation: 'God tested Abraham by asking him to sacrifice Isaac, his son of promise.',
    difficulty: 'easy',
    reference: 'Genesis 22',
    category: 'sacrifice'
  },
  {
    id: 'genbook-q5',
    question: 'What did Jacob see in his dream at Bethel?',
    options: ['Angels fighting', 'A ladder with angels ascending and descending', 'God\'s throne', 'A burning bush'],
    correctAnswer: 1,
    explanation: 'Jacob dreamed of a ladder set up on earth with its top in heaven, and angels ascending and descending.',
    difficulty: 'medium',
    reference: 'Genesis 28:12',
    category: 'dreams'
  },
  {
    id: 'genbook-q6',
    question: 'How many sons did Jacob have?',
    options: ['10', '11', '12', '13'],
    correctAnswer: 2,
    explanation: 'Jacob had twelve sons who became the twelve tribes of Israel.',
    difficulty: 'easy',
    reference: 'Genesis 35:22',
    category: 'patriarchs'
  },
  {
    id: 'genbook-q7',
    question: 'Which son was Joseph\'s full brother?',
    options: ['Judah', 'Benjamin', 'Reuben', 'Simeon'],
    correctAnswer: 1,
    explanation: 'Benjamin and Joseph were both sons of Rachel, making them full brothers.',
    difficulty: 'medium',
    reference: 'Genesis 35:24',
    category: 'family'
  },
  {
    id: 'genbook-q8',
    question: 'What did Joseph\'s brothers do to him?',
    options: ['Killed him', 'Sold him into slavery', 'Made him king', 'Sent him away'],
    correctAnswer: 1,
    explanation: 'Joseph\'s brothers sold him to Ishmaelite traders who took him to Egypt.',
    difficulty: 'easy',
    reference: 'Genesis 37:28',
    category: 'joseph'
  },
  {
    id: 'genbook-q9',
    question: 'Who was the first murderer mentioned in the Bible?',
    options: ['Adam', 'Cain', 'Lamech', 'Nimrod'],
    correctAnswer: 1,
    explanation: 'Cain killed his brother Abel, making him the first murderer recorded in Scripture.',
    difficulty: 'easy',
    reference: 'Genesis 4:8',
    category: 'sin'
  },
  {
    id: 'genbook-q10',
    question: 'How old was Noah when the flood began?',
    options: ['500 years', '600 years', '700 years', '800 years'],
    correctAnswer: 1,
    explanation: 'Noah was six hundred years old when the floodwaters came on the earth.',
    difficulty: 'medium',
    reference: 'Genesis 7:6',
    category: 'flood'
  },
  {
    id: 'genbook-q11',
    question: 'What was the sign of God\'s covenant with Abraham?',
    options: ['The rainbow', 'Circumcision', 'A sacrifice', 'A new name'],
    correctAnswer: 1,
    explanation: 'God established circumcision as the sign of His covenant with Abraham.',
    difficulty: 'medium',
    reference: 'Genesis 17:11',
    category: 'covenant'
  },
  {
    id: 'genbook-q12',
    question: 'What did God promise Abraham about his descendants?',
    options: ['They would be wise', 'They would be as numerous as the stars', 'They would be wealthy', 'They would be powerful'],
    correctAnswer: 1,
    explanation: 'God promised Abraham that his descendants would be as numerous as the stars in heaven.',
    difficulty: 'easy',
    reference: 'Genesis 15:5',
    category: 'promise'
  },
  {
    id: 'genbook-q13',
    question: 'Which city did God destroy along with Sodom?',
    options: ['Bethel', 'Gomorrah', 'Hebron', 'Beersheba'],
    correctAnswer: 1,
    explanation: 'God destroyed both Sodom and Gomorrah because of their great wickedness.',
    difficulty: 'easy',
    reference: 'Genesis 19:24',
    category: 'judgment'
  },
  {
    id: 'genbook-q14',
    question: 'What happened to Lot\'s wife when she looked back?',
    options: ['She was blinded', 'She became a pillar of salt', 'She was struck down', 'She was left behind'],
    correctAnswer: 1,
    explanation: 'Lot\'s wife looked back and became a pillar of salt.',
    difficulty: 'easy',
    reference: 'Genesis 19:26',
    category: 'judgment'
  },
  {
    id: 'genbook-q15',
    question: 'Who wrestled with Jacob all night?',
    options: ['An angel', 'A man (God)', 'Esau', 'A stranger'],
    correctAnswer: 1,
    explanation: 'Jacob wrestled with a man until daybreak, who was God in human form.',
    difficulty: 'medium',
    reference: 'Genesis 32:24',
    category: 'wrestling'
  },
  {
    id: 'genbook-q16',
    question: 'What new name did God give Jacob?',
    options: ['Abraham', 'Israel', 'Joseph', 'Benjamin'],
    correctAnswer: 1,
    explanation: 'God changed Jacob\'s name to Israel, meaning "he who wrestles with God."',
    difficulty: 'easy',
    reference: 'Genesis 32:28',
    category: 'names'
  },
  {
    id: 'genbook-q17',
    question: 'How many years of famine did Joseph predict?',
    options: ['5 years', '7 years', '10 years', '12 years'],
    correctAnswer: 1,
    explanation: 'Joseph interpreted Pharaoh\'s dream as seven years of plenty followed by seven years of famine.',
    difficulty: 'easy',
    reference: 'Genesis 41:29-30',
    category: 'dreams'
  },
  {
    id: 'genbook-q18',
    question: 'What position did Pharaoh give Joseph in Egypt?',
    options: ['Priest', 'Second in command over all Egypt', 'Captain of the guard', 'Chief butler'],
    correctAnswer: 1,
    explanation: 'Pharaoh set Joseph over all the land of Egypt, second only to Pharaoh himself.',
    difficulty: 'easy',
    reference: 'Genesis 41:41',
    category: 'joseph'
  },
  {
    id: 'genbook-q19',
    question: 'How old was Abraham when Isaac was born?',
    options: ['90 years', '100 years', '110 years', '120 years'],
    correctAnswer: 1,
    explanation: 'Abraham was one hundred years old when his son Isaac was born.',
    difficulty: 'medium',
    reference: 'Genesis 21:5',
    category: 'patriarchs'
  },
  {
    id: 'genbook-q20',
    question: 'What did Jacob give Joseph that made his brothers jealous?',
    options: ['A blessing', 'A coat of many colors', 'Land', 'Sheep'],
    correctAnswer: 1,
    explanation: 'Jacob made Joseph a coat of many colors, showing his special love for him.',
    difficulty: 'easy',
    reference: 'Genesis 37:3',
    category: 'joseph'
  },
  {
    id: 'genbook-q21',
    question: 'How long did the flood last on the earth?',
    options: ['40 days', '150 days', '1 year', '2 years'],
    correctAnswer: 1,
    explanation: 'The waters prevailed on the earth for 150 days.',
    difficulty: 'medium',
    reference: 'Genesis 7:24',
    category: 'flood'
  },
  {
    id: 'genbook-q22',
    question: 'What did God use to confuse the people at Babel?',
    options: ['Different languages', 'Darkness', 'Fire', 'Earthquake'],
    correctAnswer: 0,
    explanation: 'God confused their language so they could not understand one another.',
    difficulty: 'easy',
    reference: 'Genesis 11:7',
    category: 'babel'
  },
  {
    id: 'genbook-q23',
    question: 'Who was the mother of Isaac?',
    options: ['Hagar', 'Sarah', 'Rebekah', 'Rachel'],
    correctAnswer: 1,
    explanation: 'Sarah, Abraham\'s wife, was the mother of Isaac.',
    difficulty: 'easy',
    reference: 'Genesis 21:3',
    category: 'family'
  },
  {
    id: 'genbook-q24',
    question: 'What did Joseph\'s brothers bring to Jacob to make him think Joseph was dead?',
    options: ['Joseph\'s sandals', 'Joseph\'s coat dipped in blood', 'A false witness', 'Joseph\'s ring'],
    correctAnswer: 1,
    explanation: 'They dipped Joseph\'s coat in goat\'s blood and brought it to their father.',
    difficulty: 'medium',
    reference: 'Genesis 37:31',
    category: 'deception'
  },
  {
    id: 'genbook-q25',
    question: 'How old was Joseph when he died?',
    options: ['100 years', '110 years', '120 years', '130 years'],
    correctAnswer: 1,
    explanation: 'Joseph lived one hundred and ten years.',
    difficulty: 'hard',
    reference: 'Genesis 50:26',
    category: 'joseph'
  }
];

// Export all quiz data
export const genesisQuizzes = {
  'genesis-1': {
    id: 'genesis-1-quiz',
    title: 'Genesis 1 Quiz',
    description: 'Test your knowledge of Genesis chapter 1 - the creation account.',
    type: 'chapter' as const,
    difficulty: 'easy' as const,
    questions: genesis1Questions,
    estimatedTime: 8,
    relatedQuizzes: ['genesis-2-quiz', 'genesis-book-quiz'],
    seoTitle: 'Genesis 1 Quiz - Creation Account | Bible Quiz',
    seoDescription: 'Test your knowledge of Genesis chapter 1 with 18 questions about the creation account. Free interactive Bible quiz with instant results.',
    keywords: ['genesis 1 quiz', 'creation quiz', 'bible quiz', 'genesis chapter 1'],
    slug: 'genesis-1-quiz',
    bookId: 'genesis',
    chapter: 1
  },
  'genesis-2': {
    id: 'genesis-2-quiz',
    title: 'Genesis 2 Quiz',
    description: 'Test your knowledge of Genesis chapter 2 - the Garden of Eden.',
    type: 'chapter' as const,
    difficulty: 'easy' as const,
    questions: genesis2Questions,
    estimatedTime: 8,
    relatedQuizzes: ['genesis-1-quiz', 'genesis-3-quiz'],
    seoTitle: 'Genesis 2 Quiz - Garden of Eden | Bible Quiz',
    seoDescription: 'Test your knowledge of Genesis chapter 2 with 17 questions about the Garden of Eden. Free interactive Bible quiz with instant results.',
    keywords: ['genesis 2 quiz', 'garden of eden quiz', 'bible quiz', 'genesis chapter 2'],
    slug: 'genesis-2-quiz',
    bookId: 'genesis',
    chapter: 2
  },
  'genesis-book': {
    id: 'genesis-book-quiz',
    title: 'Genesis Quiz (Chapters 1-50)',
    description: 'Comprehensive quiz covering the entire book of Genesis.',
    type: 'book' as const,
    difficulty: 'medium' as const,
    questions: genesisBookQuestions,
    estimatedTime: 15,
    relatedQuizzes: ['exodus-quiz', 'abraham-quiz', 'joseph-quiz'],
    seoTitle: 'Genesis Quiz - Complete Bible Book Quiz | SalvationCall',
    seoDescription: 'Test your knowledge of the entire book of Genesis with 25 comprehensive questions covering creation, patriarchs, and key events.',
    keywords: ['genesis quiz', 'bible book quiz', 'genesis 1-50', 'patriarchs quiz'],
    slug: 'genesis-quiz',
    bookId: 'genesis'
  }
};

// Helper function to get quiz by slug
export function getGenesisQuizBySlug(slug: string): Quiz | undefined {
  const quizKey = Object.keys(genesisQuizzes).find(key =>
    genesisQuizzes[key as keyof typeof genesisQuizzes].slug === slug
  );
  return quizKey ? genesisQuizzes[quizKey as keyof typeof genesisQuizzes] : undefined;
}

// Helper function to generate all Genesis chapter quizzes (for chapters 3-50)
export function generateAllGenesisQuizzes(): Quiz[] {
  const allQuizzes: Quiz[] = [];

  // Add the existing quizzes
  allQuizzes.push(genesisQuizzes['genesis-book']);
  allQuizzes.push(genesisQuizzes['genesis-1']);
  allQuizzes.push(genesisQuizzes['genesis-2']);

  // Generate placeholder quizzes for chapters 3-50
  for (let chapter = 3; chapter <= 50; chapter++) {
    allQuizzes.push({
      id: `genesis-${chapter}-quiz`,
      title: `Genesis ${chapter} Quiz`,
      description: `Test your knowledge of Genesis chapter ${chapter}. All questions are compulsory and should be answered before submission.`,
      type: 'chapter',
      difficulty: 'medium',
      questions: [], // Will be populated with actual questions
      estimatedTime: 8,
      relatedQuizzes: [`genesis-${chapter-1}-quiz`, `genesis-${chapter+1}-quiz`],
      seoTitle: `Genesis ${chapter} Quiz - Bible Chapter Quiz | SalvationCall`,
      seoDescription: `Test your knowledge of Genesis chapter ${chapter} with interactive Bible quiz questions. Free instant results.`,
      keywords: [`genesis ${chapter} quiz`, 'bible quiz', `genesis chapter ${chapter}`],
      slug: `genesis-${chapter}-quiz`,
      bookId: 'genesis',
      chapter: chapter
    });
  }

  return allQuizzes;
}
