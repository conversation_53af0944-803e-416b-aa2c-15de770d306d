import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { generateAllQuizSlugs, generateQuizMetadata } from '../../lib/quiz-generator';
import QuizPageComponent from '../../components/QuizPageComponent';

interface PageProps {
  params: {
    slug: string;
  };
}

// Generate static params for all quiz pages
export async function generateStaticParams() {
  const slugs = generateAllQuizSlugs();
  
  // Return first 100 for initial build, rest will be generated on-demand with ISR
  return slugs.slice(0, 100).map((slug) => ({
    slug: slug,
  }));
}

// Generate metadata for each quiz page
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const quiz = generateQuizMetadata(params.slug);
  
  if (!quiz) {
    return {
      title: 'Quiz Not Found | SalvationCall',
      description: 'The requested Bible quiz could not be found.',
    };
  }

  return {
    title: quiz.seoTitle,
    description: quiz.seoDescription,
    keywords: quiz.keywords,
    openGraph: {
      title: quiz.seoTitle,
      description: quiz.seoDescription,
      type: 'article',
      url: `https://salvationcall.com/${quiz.slug}/`,
      siteName: 'SalvationCall',
      images: [
        {
          url: '/images/bible-quiz-og.jpg',
          width: 1200,
          height: 630,
          alt: quiz.title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: quiz.seoTitle,
      description: quiz.seoDescription,
      images: ['/images/bible-quiz-og.jpg'],
    },
    alternates: {
      canonical: `https://salvationcall.com/${quiz.slug}/`,
    },
  };
}

export default function QuizPageRoute({ params }: PageProps) {
  const quiz = generateQuizMetadata(params.slug);
  
  if (!quiz) {
    notFound();
  }

  return <QuizPageComponent quiz={quiz} />;
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600;
