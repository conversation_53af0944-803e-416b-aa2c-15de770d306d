import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";
import Header from "../components/Header";
import Footer from "../components/Footer";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: {
    default: "SalvationCall - Bible Quizzes & Study Resources",
    template: "%s | SalvationCall"
  },
  description: "Test your Bible knowledge with our comprehensive collection of interactive Bible quizzes. Covering all 66 books, characters, themes, and more. Perfect for all ages and skill levels.",
  keywords: [
    "bible quiz", "bible study", "scripture test", "bible knowledge",
    "christian education", "biblical literacy", "sunday school",
    "genesis quiz", "psalms quiz", "new testament quiz", "old testament quiz",
    "bible trivia", "christian learning", "bible study tools", "scripture quiz"
  ],
  authors: [{ name: "SalvationCall" }],
  creator: "SalvationCall",
  publisher: "SalvationCall",
  metadataBase: new URL("https://salvationcall.com"),
  alternates: {
    canonical: "https://salvationcall.com",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://salvationcall.com",
    siteName: "SalvationCall",
    title: "SalvationCall - Bible Quizzes & Study Resources",
    description: "Test your Bible knowledge with our comprehensive collection of interactive Bible quizzes covering all 66 books, characters, and themes.",
    images: [
      {
        url: "/images/bible-quiz-og.jpg",
        width: 1200,
        height: 630,
        alt: "SalvationCall Bible Quizzes - Test Your Scripture Knowledge",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "SalvationCall - Bible Quizzes & Study Resources",
    description: "Test your Bible knowledge with our comprehensive collection of interactive Bible quizzes.",
    images: ["/images/bible-quiz-og.jpg"],
    creator: "@salvationcall",
  },
  verification: {
    google: "your-google-verification-code",
  },
  category: "education",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "SalvationCall",
              "url": "https://salvationcall.com",
              "logo": "https://salvationcall.com/images/logo.png",
              "description": "Comprehensive Bible quiz platform for Christian education and Scripture study",
              "sameAs": [
                "https://twitter.com/salvationcall",
                "https://facebook.com/salvationcall"
              ],
              "potentialAction": {
                "@type": "SearchAction",
                "target": "https://salvationcall.com/bible-quizzes?search={search_term_string}",
                "query-input": "required name=search_term_string"
              }
            })
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <Header />
        <main className="flex-grow">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
