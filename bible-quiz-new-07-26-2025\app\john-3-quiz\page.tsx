import { Metadata } from 'next';
import QuizPageComponent from '../../components/QuizPageComponent';
import { sampleQuizzes } from '../../data/sample-quizzes';

export const metadata: Metadata = {
  title: 'John 3 Quiz - Test Your Bible Knowledge | SalvationCall',
  description: 'Test your knowledge of John chapter 3 with this interactive Bible quiz. 5 questions covering Nicodemus, being born again, and John 3:16.',
  keywords: ['john quiz', 'bible quiz', 'john chapter 3', 'nicodemus', 'born again', 'john 3:16'],
  openGraph: {
    title: 'John 3 Quiz - Test Your Bible Knowledge | SalvationCall',
    description: 'Test your knowledge of John chapter 3 with this interactive Bible quiz.',
    type: 'article',
    url: 'https://salvationcall.com/john-3-quiz/',
    siteName: 'SalvationCall',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'John 3 Quiz - Test Your Bible Knowledge | SalvationCall',
    description: 'Test your knowledge of <PERSON> chapter 3 with this interactive Bible quiz.',
  },
  alternates: {
    canonical: 'https://salvationcall.com/john-3-quiz/',
  },
};

export default function John3QuizPage() {
  const quiz = sampleQuizzes.find(q => q.id === 'john-3-quiz');
  
  if (!quiz) {
    return <div>Quiz not found</div>;
  }

  return <QuizPageComponent quiz={quiz} />;
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600;
