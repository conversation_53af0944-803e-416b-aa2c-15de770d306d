'use client';

import Link from 'next/link';
import { BibleBook } from '../types/bible';
import { ChevronRightIcon } from '@heroicons/react/24/outline';

interface BookQuizListingPageProps {
  bookData: BibleBook;
}

interface QuizItem {
  title: string;
  description: string;
  href: string;
  difficulty: string;
  questions: number;
  time: string;
  isBookQuiz?: boolean;
}

export default function BookQuizListingPage({ bookData }: BookQuizListingPageProps) {
  // Generate all chapter quizzes for this book
  const generateChapterQuizzes = (): QuizItem[] => {
    const quizzes: QuizItem[] = [];

    // Add the full book quiz first
    quizzes.push({
      title: `${bookData.name} Quiz (Chapters 1-${bookData.chapters})`,
      description: `Comprehensive quiz covering the entire book of ${bookData.name}. Test your knowledge of key events, characters, and themes across all ${bookData.chapters} chapters.`,
      href: `/${bookData.slug}-quiz/?quiz=book`,
      difficulty: 'Hard',
      questions: 15,
      time: '20 minutes',
      isBookQuiz: true
    });

    // Add individual chapter quizzes in reverse order (50, 49, 48... down to 1)
    for (let chapter = bookData.chapters; chapter >= 1; chapter--) {
      quizzes.push({
        title: `${bookData.name} ${chapter} Quiz`,
        description: `Test your knowledge of ${bookData.name} chapter ${chapter}. Focused questions on key verses, events, and teachings from this specific chapter.`,
        href: `/${bookData.slug}-${chapter}-quiz/?start=true`,
        difficulty: chapter <= 10 ? 'Easy' : chapter <= 30 ? 'Medium' : 'Hard',
        questions: 10,
        time: '10 minutes'
      });
    }

    return quizzes;
  };

  const allQuizzes = generateChapterQuizzes();

  // Generate breadcrumbs
  const breadcrumbs = [
    { name: 'Home', url: '/' },
    { name: 'Bible Quizzes', url: '/quizzes' },
    { name: `${bookData.name} Quiz`, url: `/${bookData.slug}-quiz/` }
  ];

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Breadcrumbs */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            {breadcrumbs.map((crumb, index) => (
              <li key={index} className="flex items-center">
                {index > 0 && <span className="text-gray-400 mx-2">/</span>}
                {index === breadcrumbs.length - 1 ? (
                  <span className="text-gray-600 font-medium">{crumb.name}</span>
                ) : (
                  <Link href={crumb.url} className="text-blue-600 hover:text-blue-800">
                    {crumb.name}
                  </Link>
                )}
              </li>
            ))}
          </ol>
        </div>
      </nav>

      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Bible Quiz {bookData.name} 1-{bookData.chapters}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {bookData.description} Choose from comprehensive book quizzes or focused chapter-by-chapter studies.
            </p>
          </div>
        </div>
      </div>

      {/* Quiz Listings */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="space-y-6">
          {allQuizzes.map((quizItem, index) => (
            <article key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                      <Link href={quizItem.href} className="hover:text-blue-600 transition-colors">
                        {quizItem.title}
                      </Link>
                    </h2>
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {quizItem.description}
                    </p>
                    <div className="flex items-center space-x-6 text-sm text-gray-500 mb-4">
                      <span className="flex items-center">
                        <span className="font-medium">{quizItem.questions} Questions</span>
                      </span>
                      <span className="flex items-center">
                        <span className="font-medium">{quizItem.time}</span>
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        quizItem.difficulty === 'Easy' ? 'bg-green-100 text-green-800' :
                        quizItem.difficulty === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {quizItem.difficulty}
                      </span>
                      {quizItem.isBookQuiz && (
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Book Quiz
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="ml-6 flex-shrink-0">
                    <Link
                      href={quizItem.href}
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Take Quiz
                      <ChevronRightIcon className="ml-2 h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* Pagination placeholder (matching SalvationCall style) */}
        <div className="mt-12 flex justify-center">
          <div className="flex items-center space-x-2">
            <span className="px-3 py-2 text-sm text-gray-500">
              Showing all {allQuizzes.length} quizzes
            </span>
          </div>
        </div>
      </div>

      {/* SEO Schema Markup */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ItemList",
            "name": `${bookData.name} Bible Quizzes`,
            "description": `Complete collection of Bible quizzes for the book of ${bookData.name}`,
            "url": `https://salvationcall.com/${bookData.slug}-quiz/`,
            "publisher": {
              "@type": "Organization",
              "name": "SalvationCall",
              "url": "https://salvationcall.com"
            },
            "mainEntity": {
              "@type": "ItemList",
              "numberOfItems": allQuizzes.length,
              "itemListElement": allQuizzes.map((quizItem, index) => ({
                "@type": "Quiz",
                "position": index + 1,
                "name": quizItem.title,
                "description": quizItem.description,
                "url": `https://salvationcall.com${quizItem.href}`
              }))
            }
          })
        }}
      />
    </div>
  );
}
