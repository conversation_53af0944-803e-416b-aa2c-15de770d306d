import { Metadata } from 'next';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

export const metadata: Metadata = {
  title: 'Frequently Asked Questions - Bible Quiz Help',
  description: 'Get answers to common questions about our Bible quizzes, scoring system, difficulty levels, and how to improve your Scripture knowledge.',
  keywords: ['bible quiz faq', 'bible study help', 'quiz questions', 'scripture learning'],
  alternates: {
    canonical: 'https://salvationcall.com/faq/',
  },
};

const faqs = [
  {
    question: "How many Bible quizzes are available on SalvationCall?",
    answer: "We offer over 1,000 Bible quizzes covering all 66 books of the Bible, major biblical characters, themes, and topics. Our collection includes quizzes for every chapter, book overviews, character studies, and thematic quizzes."
  },
  {
    question: "What difficulty levels are available?",
    answer: "Our quizzes are categorized into three difficulty levels: Easy (perfect for beginners and children), Medium (for regular Bible readers), and Hard (for advanced Bible students and scholars). Each quiz clearly displays its difficulty level."
  },
  {
    question: "How is the scoring system calculated?",
    answer: "Your score is calculated as a percentage based on correct answers. You'll receive instant feedback for each question with detailed explanations. Scores of 90%+ are excellent, 80-89% are great, 70-79% are good, and below 70% indicates areas for further study."
  },
  {
    question: "Are the quizzes based on a specific Bible translation?",
    answer: "Our quizzes are primarily based on the King James Version (KJV) but are designed to be accessible to readers of all major Bible translations. We focus on core biblical truths and facts that remain consistent across translations."
  },
  {
    question: "Can I retake quizzes to improve my score?",
    answer: "Absolutely! You can retake any quiz as many times as you'd like. This is a great way to reinforce your learning and improve your Bible knowledge over time."
  },
  {
    question: "How long do quizzes typically take to complete?",
    answer: "Most quizzes take 5-15 minutes to complete, depending on the number of questions and difficulty level. Chapter quizzes are usually shorter (5-10 minutes), while book and thematic quizzes may take longer (10-15 minutes)."
  },
  {
    question: "Do I need to create an account to take quizzes?",
    answer: "No account is required! All our Bible quizzes are completely free and accessible without registration. Simply visit any quiz page and start learning immediately."
  },
  {
    question: "Are the quizzes suitable for children?",
    answer: "Yes! We have many easy-level quizzes that are perfect for children, Sunday school classes, and Bible study groups. The content is appropriate for all ages and designed to encourage learning at every level."
  },
  {
    question: "How can I find quizzes on specific Bible books or topics?",
    answer: "Use our Bible Quizzes page which includes search and filter functionality. You can filter by Testament (Old/New), difficulty level, quiz type (chapter, book, character, theme), or search for specific topics."
  },
  {
    question: "What makes SalvationCall different from other Bible quiz sites?",
    answer: "We offer the most comprehensive collection of Bible quizzes available online, with detailed explanations for every answer, multiple difficulty levels, and a focus on educational value rather than just entertainment."
  }
];

export default function FAQPage() {
  return (
    <div className="bg-gray-50 min-h-screen py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Get answers to common questions about our Bible quizzes and how to make the most of your Scripture study experience.
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <details key={index} className="bg-white rounded-lg shadow-md">
              <summary className="flex justify-between items-center p-6 cursor-pointer hover:bg-gray-50 transition-colors">
                <h3 className="text-lg font-semibold text-gray-900 pr-4">
                  {faq.question}
                </h3>
                <ChevronDownIcon className="h-5 w-5 text-gray-500 flex-shrink-0 transform transition-transform duration-200" />
              </summary>
              <div className="px-6 pb-6">
                <p className="text-gray-700 leading-relaxed">
                  {faq.answer}
                </p>
              </div>
            </details>
          ))}
        </div>

        {/* Call to Action */}
        <div className="mt-12 text-center">
          <div className="bg-blue-600 text-white rounded-xl p-8">
            <h2 className="text-2xl font-bold mb-4">Ready to Test Your Bible Knowledge?</h2>
            <p className="text-blue-100 mb-6">
              Start with our most popular quizzes or browse our complete collection.
            </p>
            <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
              <a
                href="/genesis-1-quiz"
                className="inline-block bg-yellow-500 text-blue-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-colors"
              >
                Take Genesis 1 Quiz
              </a>
              <a
                href="/bible-quizzes"
                className="inline-block bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Browse All Quizzes
              </a>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="mt-12 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Still Have Questions?</h2>
          <p className="text-gray-600 mb-6">
            Can&apos;t find the answer you&apos;re looking for? We&apos;d love to help you on your Bible study journey.
          </p>
          <a
            href="mailto:<EMAIL>"
            className="inline-block bg-gray-900 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors"
          >
            Contact Support
          </a>
        </div>
      </div>

      {/* FAQ Schema Markup */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": faqs.map(faq => ({
              "@type": "Question",
              "name": faq.question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": faq.answer
              }
            }))
          })
        }}
      />
    </div>
  );
}
