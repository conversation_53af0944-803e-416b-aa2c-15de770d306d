// Server-side component (remove 'use client')

import Link from 'next/link';
import { ArrowLeftIcon, ArrowRightIcon, BookOpenIcon, HomeIcon } from '@heroicons/react/24/outline';
import { Quiz } from '../types/bible';

interface InternalLinksSectionProps {
  quiz: Quiz;
  title: string;
  showBeforeQuiz?: boolean;
}

// Helper function to generate internal links based on quiz type
function generateInternalLinks(quiz: Quiz) {
  const links = [];

  // Extract book and chapter info from quiz ID
  const quizIdParts = quiz.id.split('-');
  const bookName = quizIdParts[0]; // e.g., 'genesis'
  const chapterNum = quizIdParts[1] === 'book' ? null : parseInt(quizIdParts[1]); // e.g., 1, 2, etc.

  // Capitalize book name for display
  const bookDisplayName = bookName.charAt(0).toUpperCase() + bookName.slice(1);

  if (chapterNum) {
    // This is a chapter quiz

    // Study guide link
    links.push({
      type: 'study',
      text: `Study ${bookDisplayName} Chapter ${chapterNum} first`,
      href: `/${bookName}-${chapterNum}-study/`,
      icon: BookOpenIcon,
      description: 'Read the chapter before taking the quiz'
    });

    // Previous chapter quiz (if not chapter 1)
    if (chapterNum > 1) {
      links.push({
        type: 'previous',
        text: `← ${bookDisplayName} Chapter ${chapterNum - 1} Quiz`,
        href: `/${bookName}-${chapterNum - 1}-quiz/`,
        icon: ArrowLeftIcon,
        description: 'Take the previous chapter quiz'
      });
    }

    // Next chapter quiz
    links.push({
      type: 'next',
      text: `${bookDisplayName} Chapter ${chapterNum + 1} Quiz →`,
      href: `/${bookName}-${chapterNum + 1}-quiz/`,
      icon: ArrowRightIcon,
      description: 'Continue to the next chapter'
    });

    // Full book quiz
    links.push({
      type: 'book',
      text: `Take the complete ${bookDisplayName} Quiz`,
      href: `/${bookName}-book-quiz/`,
      icon: BookOpenIcon,
      description: 'Test your knowledge of the entire book'
    });
  }

  // Main quiz hub (always present)
  links.push({
    type: 'hub',
    text: 'Browse all Bible Quizzes',
    href: '/bible-quizzes/',
    icon: HomeIcon,
    description: 'Explore quizzes from all books of the Bible'
  });

  return links;
}

export default function InternalLinksSection({ quiz, title, showBeforeQuiz = false }: InternalLinksSectionProps) {
  const internalLinks = generateInternalLinks(quiz);

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
          <BookOpenIcon className="h-5 w-5 mr-2" />
          {title}
        </h3>
        <div className="text-sm text-blue-800 mb-4">
          Contextual Linking Patterns - Every Quiz Page Must Link To:
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {internalLinks.map((link, index) => {
            const IconComponent = link.icon;
            return (
              <Link
                key={index}
                href={link.href}
                className="flex items-center p-3 bg-white border border-blue-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors group"
              >
                <IconComponent className="h-4 w-4 text-blue-600 mr-3 flex-shrink-0" />
                <div className="flex-1">
                  <div className="text-sm font-medium text-blue-900 group-hover:text-blue-700">
                    {link.text}
                  </div>
                  <div className="text-xs text-blue-600 mt-1">
                    {link.description}
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
}