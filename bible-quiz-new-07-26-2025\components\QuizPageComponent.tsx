'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { BookOpenIcon, UserIcon, TagIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { Quiz } from '../types/bible';
import { generateBreadcrumbs } from '../lib/quiz-generator';
import { bibleBooks } from '../data/bible-books';
import dynamic from 'next/dynamic';
import InternalLinksSection from './InternalLinksSection';

// Dynamically import InteractiveQuiz with SSR disabled to prevent hydration errors
const InteractiveQuiz = dynamic(() => import('./InteractiveQuiz'), {
  ssr: false,
  loading: () => (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="bg-white rounded-xl shadow-lg p-8 text-center">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="h-4 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
        </div>
        <div className="mt-8">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-600 border-r-transparent"></div>
          <p className="mt-2 text-gray-600">Loading quiz...</p>
        </div>
      </div>
    </div>
  )
});
import { sampleQuizzes } from '../data/sample-quizzes';

interface QuizPageProps {
  quiz: Quiz;
}

export default function QuizPageComponent({ quiz }: QuizPageProps) {
  const searchParams = useSearchParams();
  const breadcrumbs = generateBreadcrumbs(quiz);

  // Check if this is a book-level quiz (e.g., genesis-quiz, matthew-quiz)
  const isBookQuiz = quiz.type === 'book';
  const bookData = isBookQuiz ? bibleBooks.find(book => book.slug === quiz.slug.replace('-quiz', '')) : null;

  // Get actual questions for this quiz
  const quizWithQuestions = sampleQuizzes.find(q => q.id === quiz.id);
  const questions = quizWithQuestions?.questions || [];

  // State management for quiz display - ensure consistent initial state
  const [showQuiz, setShowQuiz] = useState(() => {
    // For non-book quizzes, always auto-start
    return !isBookQuiz;
  });

  // Handle URL parameter changes after hydration
  useEffect(() => {
    // For non-book quizzes, ensure quiz is shown
    if (!isBookQuiz) {
      setShowQuiz(true);
      return;
    }
    
    // For book quizzes, check start parameter
    const startParam = searchParams.get('start');
    setShowQuiz(startParam === 'true');
  }, [isBookQuiz, searchParams]);

  // Generate chapter quizzes for book quizzes
  const generateChapterQuizzes = () => {
    if (!bookData) return [];

    const quizzes = [];

    // Add the full book quiz first
    quizzes.push({
      title: `${bookData.name} Quiz (Chapters 1-${bookData.chapters})`,
      description: `Comprehensive quiz covering the entire book of ${bookData.name}. All questions are compulsory and should be answered before submission. Select the correct answers from the options given. We wish you all the best.`,
      href: `/${bookData.slug}-quiz/?quiz=book`,
      questions: 25,
      time: '15-20 min',
      difficulty: 'Medium',
      isBookQuiz: true,
      isFullBook: true
    });

    // Add individual chapter quizzes in reverse order (latest first)
    for (let i = bookData.chapters; i >= 1; i--) {
      quizzes.push({
        title: `${bookData.name} ${i} Quiz`,
        description: `This quiz is based on ${bookData.name} chapter ${i}. All questions are compulsory and should be answered before submission. Select the correct answers from the options given. We wish you all the best.`,
        href: `/${bookData.slug}-${i}-quiz/?start=true`,
        questions: 10,
        time: '5-8 min',
        difficulty: 'Easy',
        isBookQuiz: false,
        isFullBook: false,
        chapter: i
      });
    }

    return quizzes;
  };
  
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'book':
        return BookOpenIcon;
      case 'character':
        return UserIcon;
      case 'theme':
        return TagIcon;
      default:
        return BookOpenIcon;
    }
  };

  const TypeIcon = getTypeIcon(quiz.type);


  // Handle interactive quiz display with server-rendered Internal Links
  if (!isBookQuiz && showQuiz) {
    return (
      <>
        {/* Server-rendered Internal Links Section */}
        <InternalLinksSection quiz={quiz} title="Internal Linking Strategy" showBeforeQuiz={true} />
        
        {/* Client-rendered Quiz Component with Suspense boundary */}
        <div suppressHydrationWarning>
          <InteractiveQuiz quiz={quiz} questions={questions} />
        </div>
      </>
    );
  }

  // Handle book quiz listing
  if (isBookQuiz && bookData && !showQuiz) {
    const allQuizzes = generateChapterQuizzes();

    return (
      <div className="bg-gray-50 min-h-screen">
        {/* Breadcrumbs */}
        <nav className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <ol className="flex items-center space-x-2 text-sm">
              <li className="flex items-center">
                <Link href="/" className="text-blue-600 hover:text-blue-800">Home</Link>
              </li>
              <li className="flex items-center">
                <span className="text-gray-400 mx-2">/</span>
                <Link href="/quizzes" className="text-blue-600 hover:text-blue-800">Bible Quizzes</Link>
              </li>
              <li className="flex items-center">
                <span className="text-gray-400 mx-2">/</span>
                <span className="text-gray-600 font-medium">{bookData.name} Quiz</span>
              </li>
            </ol>
          </div>
        </nav>

        {/* Page Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Bible Quiz {bookData.name} 1-{bookData.chapters}
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {bookData.description} Choose from comprehensive book quizzes or focused chapter-by-chapter studies.
              </p>
            </div>
          </div>
        </div>

        {/* Quiz Listings */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {allQuizzes.map((quizItem, index) => (
              <article key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h2 className="text-xl font-semibold text-gray-900 mb-2">
                        <Link href={quizItem.href} className="hover:text-blue-600 transition-colors">
                          {quizItem.title}
                        </Link>
                      </h2>
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {quizItem.description}
                      </p>
                      <div className="flex items-center space-x-6 text-sm text-gray-500 mb-4">
                        <span className="flex items-center">
                          <span className="font-medium">{quizItem.questions} Questions</span>
                        </span>
                        <span className="flex items-center">
                          <span className="font-medium">{quizItem.time}</span>
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          quizItem.difficulty === 'Easy' ? 'bg-green-100 text-green-800' :
                          quizItem.difficulty === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {quizItem.difficulty}
                        </span>
                        {quizItem.isBookQuiz && (
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Book Quiz
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="ml-6 flex-shrink-0">
                      <Link
                        href={quizItem.href}
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        Take Quiz
                        <ChevronRightIcon className="ml-2 h-4 w-4" />
                      </Link>
                    </div>
                  </div>
                </div>
              </article>
            ))}
          </div>

          {/* Pagination placeholder */}
          <div className="mt-12 flex justify-center">
            <div className="flex items-center space-x-2">
              <span className="px-3 py-2 text-sm text-gray-500">
                Showing all {allQuizzes.length} quizzes
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Default quiz description page (for non-book quizzes when not showing quiz, or book quizzes when showing quiz)
  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Breadcrumbs */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            {breadcrumbs.map((crumb, index) => (
              <li key={index} className="flex items-center">
                {index > 0 && <span className="text-gray-400 mx-2">/</span>}
                {index === breadcrumbs.length - 1 ? (
                  <span className="text-gray-600 font-medium">{crumb.name}</span>
                ) : (
                  <Link href={crumb.url} className="text-blue-600 hover:text-blue-800">
                    {crumb.name}
                  </Link>
                )}
              </li>
            ))}
          </ol>
        </div>
      </nav>

      {/* Quiz Header */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <TypeIcon />
            </div>
            <h1 className="text-4xl sm:text-5xl font-bold mb-4">{quiz.title}</h1>
            <p className="text-xl text-blue-100 mb-6 max-w-3xl mx-auto">
              {quiz.description}
            </p>

            {/* Quiz Stats */}
            <div className="flex flex-wrap justify-center gap-6 mb-8">
              <div className="flex items-center space-x-2">
                <span>❓</span>
                <span>{quiz.questions.length || 10} Questions</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>⏰</span>
                <span>{quiz.estimatedTime} Minutes</span>
              </div>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(quiz.difficulty)}`}>
                {quiz.difficulty.charAt(0).toUpperCase() + quiz.difficulty.slice(1)}
              </div>
              <div className="bg-white/20 px-3 py-1 rounded-full text-sm font-medium">
                {quiz.type.charAt(0).toUpperCase() + quiz.type.slice(1)} Quiz
              </div>
            </div>

            <button
              onClick={() => {
                setShowQuiz(true);
                // Update URL to include start parameter for consistency
                if (!searchParams.get('start')) {
                  const newUrl = new URL(window.location.href);
                  newUrl.searchParams.set('start', 'true');
                  window.history.replaceState({}, '', newUrl.toString());
                }
              }}
              className="bg-yellow-500 text-blue-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-yellow-400 transition-colors"
            >
              Start Quiz Now
            </button>
          </div>
        </div>
      </section>

      {/* Quiz Content */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">About This Quiz</h2>

            <div className="prose prose-lg max-w-none mb-8">
              <p className="text-gray-600 leading-relaxed">
                {quiz.description} This interactive quiz is designed to test your knowledge and help you learn more about Scripture.
                Each question includes detailed explanations to enhance your understanding.
              </p>
            </div>

            {/* Quiz Features */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-blue-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">What You&apos;ll Learn</h3>
                <ul className="space-y-2 text-blue-800">
                  <li>• Key biblical concepts and themes</li>
                  <li>• Important verses and their meanings</li>
                  <li>• Historical and cultural context</li>
                  <li>• Practical applications for today</li>
                </ul>
              </div>

              <div className="bg-green-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-green-900 mb-3">Quiz Features</h3>
                <ul className="space-y-2 text-green-800">
                  <li>• Multiple choice questions</li>
                  <li>• Instant feedback and explanations</li>
                  <li>• Progress tracking</li>
                  <li>• Detailed results and recommendations</li>
                </ul>
              </div>
            </div>

            {/* Start Quiz Button */}
            <div className="text-center">
              <button
                onClick={() => {
                  setShowQuiz(true);
                  // Update URL to include start parameter for consistency
                  if (!searchParams.get('start')) {
                    const newUrl = new URL(window.location.href);
                    newUrl.searchParams.set('start', 'true');
                    window.history.replaceState({}, '', newUrl.toString());
                  }
                }}
                className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-700 transition-colors"
              >
                Begin Quiz
              </button>
              <p className="text-gray-500 text-sm mt-2">
                No registration required • Free to use • Mobile friendly
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Related Quizzes */}
      {quiz.relatedQuizzes && quiz.relatedQuizzes.length > 0 && (
        <section className="py-12 bg-gray-100">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">Related Quizzes</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {quiz.relatedQuizzes.slice(0, 3).map((relatedSlug, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {relatedSlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Continue your Bible study journey with this related quiz.
                  </p>
                  <Link
                    href={`/${relatedSlug}`}
                    className="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  >
                    Take Quiz
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* SEO Schema Markup */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Quiz",
            "name": quiz.title,
            "description": quiz.description,
            "about": {
              "@type": "Thing",
              "name": "Bible Study"
            },
            "educationalLevel": quiz.difficulty,
            "timeRequired": `PT${quiz.estimatedTime}M`,
            "numberOfQuestions": quiz.questions.length || 10,
            "publisher": {
              "@type": "Organization",
              "name": "SalvationCall",
              "url": "https://salvationcall.com"
            },
            "mainEntity": {
              "@type": "Question",
              "name": quiz.title,
              "text": quiz.description
            }
          })
        }}
      />
    </div>
  );
}