'use client';

import { useState, useEffect } from 'react';
import { CheckCircleIcon, ClockIcon, BookOpenIcon } from '@heroicons/react/24/outline';
import { Quiz, QuizQuestion } from '../types/bible';
import Link from 'next/link';

interface InteractiveQuizProps {
  quiz: Quiz;
  questions: QuizQuestion[];
}

interface QuizState {
  answers: (number | null)[];
  isComplete: boolean;
  score: number;
  timeElapsed: number;
  showResults: boolean;
}


export default function InteractiveQuiz({ quiz, questions }: InteractiveQuizProps) {
  // Single hydration state to prevent multiple re-renders
  const [isClient, setIsClient] = useState(false);

  // Initialize client state only after hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Show message if no questions are available
  if (questions.length === 0) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-xl shadow-lg p-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{quiz.title}</h1>
          <div className="text-xl text-gray-600 mb-6">
            Questions for this quiz are coming soon!
          </div>
          <p className="text-gray-500 mb-8">
            We're working on adding questions for this chapter. Please check back later or try other available quizzes.
          </p>
          <button
            onClick={() => isClient && window.history.back()}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // Initialize state with safe defaults that match server rendering
  const [quizState, setQuizState] = useState<QuizState>(() => ({
    answers: new Array(questions.length).fill(null),
    isComplete: false,
    score: 0,
    timeElapsed: 0,
    showResults: false,
  }));

  const [startTime, setStartTime] = useState<number | null>(null);

  // Initialize timer only on client side after hydration
  useEffect(() => {
    if (isClient && startTime === null) {
      setStartTime(Date.now());
    }
  }, [isClient, startTime]);

  // Timer effect - only run on client side
  useEffect(() => {
    if (!quizState.showResults && startTime !== null && isClient) {
      const timer = setInterval(() => {
        setQuizState(prev => ({
          ...prev,
          timeElapsed: Math.floor((Date.now() - startTime) / 1000)
        }));
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [startTime, quizState.showResults, isClient]);

  const handleAnswerSelect = (questionIndex: number, answerIndex: number) => {
    const newAnswers = [...quizState.answers];
    newAnswers[questionIndex] = answerIndex;

    setQuizState(prev => ({
      ...prev,
      answers: newAnswers
    }));
  };

  const handleSubmitQuiz = () => {
    // Calculate final score
    const score = quizState.answers.reduce((total: number, answer, index) => {
      return total + (answer !== null && answer === questions[index].correctAnswer ? 1 : 0);
    }, 0);

    setQuizState(prev => ({
      ...prev,
      isComplete: true,
      score,
      showResults: true
    }));
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getScoreColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreMessage = (percentage: number) => {
    if (percentage >= 90) return 'Excellent! You have a strong knowledge of Scripture.';
    if (percentage >= 80) return 'Great job! You know your Bible well.';
    if (percentage >= 70) return 'Good work! Keep studying to improve further.';
    if (percentage >= 60) return 'Not bad! There\'s room for improvement.';
    return 'Keep studying! Every step in learning Scripture is valuable.';
  };

  const isQuizComplete = quizState.answers.every(answer => answer !== null);

  if (quizState.showResults) {
    const percentage = Math.round((quizState.score / questions.length) * 100);
    
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-xl shadow-lg p-8 text-center">
          <div className="mb-6">
            <div className={`text-6xl font-bold mb-2 ${getScoreColor(percentage)}`}>
              {percentage}%
            </div>
            <div className="text-xl text-gray-600 mb-4">
              {quizState.score} out of {questions.length} correct
            </div>
            <div className="text-lg text-gray-700 mb-6">
              {getScoreMessage(percentage)}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{quizState.score}</div>
              <div className="text-blue-800">Correct Answers</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">
                {isClient ? formatTime(quizState.timeElapsed) : '0:00'}
              </div>
              <div className="text-gray-800">Time Taken</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{percentage}%</div>
              <div className="text-green-800">Score</div>
            </div>
          </div>

          {/* Detailed Results */}
          <div className="mb-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Review Your Answers</h3>
            <div className="space-y-6">
              {questions.map((question, index) => {
                const userAnswer = quizState.answers[index];
                const isCorrect = userAnswer === question.correctAnswer;

                return (
                  <div key={index} className="bg-gray-50 rounded-lg p-6">
                    <div className="flex items-start justify-between mb-4">
                      <h4 className="text-lg font-medium text-gray-900 flex-1">
                        <span className="text-blue-600 mr-2">Q{index + 1}.</span>
                        {question.question}
                      </h4>
                      {isCorrect ? (
                        <CheckCircleIcon className="h-6 w-6 text-green-600 flex-shrink-0 ml-4" />
                      ) : (
                        <div className="h-6 w-6 rounded-full bg-red-100 border-2 border-red-500 flex-shrink-0 ml-4"></div>
                      )}
                    </div>

                    <div className="space-y-2 mb-4">
                      {question.options.map((option, optionIndex) => {
                        const isUserAnswer = userAnswer === optionIndex;
                        const isCorrectAnswer = optionIndex === question.correctAnswer;

                        let className = 'p-3 rounded border-2 ';
                        if (isCorrectAnswer) {
                          className += 'border-green-500 bg-green-50 text-green-800';
                        } else if (isUserAnswer && !isCorrectAnswer) {
                          className += 'border-red-500 bg-red-50 text-red-800';
                        } else {
                          className += 'border-gray-200 bg-white text-gray-700';
                        }

                        return (
                          <div key={optionIndex} className={className}>
                            <div className="flex items-center justify-between">
                              <span>{option}</span>
                              <div className="flex items-center space-x-2">
                                {isUserAnswer && <span className="text-sm font-medium">Your Answer</span>}
                                {isCorrectAnswer && <span className="text-sm font-medium">Correct</span>}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
                      <h5 className="font-medium text-blue-800 mb-2">Explanation</h5>
                      <p className="text-blue-700 mb-2">{question.explanation}</p>
                      <p className="text-sm text-blue-600 font-medium">Reference: {question.reference}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Note: Internal Links now handled by parent component */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
              <BookOpenIcon className="h-5 w-5 mr-2" />
              Continue Your Bible Study Journey
            </h3>
            <div className="text-blue-800">
              Check the links above to continue your study or explore related quizzes.
            </div>
          </div>

          <div className="space-y-4 mb-8">
            <h3 className="text-xl font-semibold text-gray-900">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={() => isClient && window.scrollTo({ top: 0, behavior: 'smooth' })}
                className="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
              >
                Back to Top
              </button>
              <button
                onClick={() => isClient && window.location.reload()}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Retake Quiz
              </button>
            </div>
          </div>

          <div className="text-sm text-gray-500">
            Share your results and challenge your friends to beat your score!
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Internal Links now handled by parent component */}
      
      {/* Quiz Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-3xl font-bold text-gray-900">{quiz.title}</h1>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <ClockIcon className="h-4 w-4" />
            <span>
              {isClient ? formatTime(quizState.timeElapsed) : '0:00'}
            </span>
          </div>
        </div>
        <div className="flex justify-between items-center mb-4">
          <span className="text-lg font-medium text-gray-700">
            {questions.length} Questions
          </span>
          <div className="text-sm text-gray-600">
            Answered: {quizState.answers.filter(a => a !== null).length} / {questions.length}
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(quizState.answers.filter(a => a !== null).length / questions.length) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* All Questions */}
      <div className="space-y-8 mb-8">
        {questions.map((question, questionIndex) => (
          <div key={questionIndex} className="bg-white rounded-xl shadow-lg p-8">
            <div className="flex items-start justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900 flex-1">
                <span className="text-blue-600 mr-2">Q{questionIndex + 1}.</span>
                {question.question}
              </h2>
              {quizState.answers[questionIndex] !== null && (
                <CheckCircleIcon className="h-6 w-6 text-green-600 flex-shrink-0 ml-4" />
              )}
            </div>

            {/* Answer Options */}
            <div className="space-y-3">
              {question.options.map((option, optionIndex) => {
                const isSelected = quizState.answers[questionIndex] === optionIndex;

                let buttonClass = 'w-full p-4 text-left border-2 rounded-lg transition-all duration-200 ';

                if (isSelected) {
                  buttonClass += 'border-blue-500 bg-blue-50 text-blue-800';
                } else {
                  buttonClass += 'border-gray-300 hover:border-blue-300 hover:bg-blue-50';
                }

                return (
                  <button
                    key={optionIndex}
                    onClick={() => handleAnswerSelect(questionIndex, optionIndex)}
                    className={buttonClass}
                  >
                    <div className="flex items-center">
                      <div className={`w-6 h-6 rounded-full border-2 mr-3 flex items-center justify-center ${
                        isSelected ? 'border-blue-500 bg-blue-500' : 'border-gray-300'
                      }`}>
                        {isSelected && <div className="w-2 h-2 bg-white rounded-full"></div>}
                      </div>
                      <span className="font-medium">{option}</span>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Submit Button */}
      <div className="text-center">
        <button
          onClick={handleSubmitQuiz}
          disabled={!isQuizComplete}
          className={`px-8 py-4 rounded-lg font-semibold text-lg transition-colors ${
            isQuizComplete
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          {isQuizComplete ? 'Submit Quiz' : `Answer All Questions (${quizState.answers.filter(a => a !== null).length}/${questions.length})`}
        </button>
        {!isQuizComplete && (
          <p className="text-sm text-gray-600 mt-2">
            Please answer all questions before submitting.
          </p>
        )}
      </div>
    </div>
  );
}
