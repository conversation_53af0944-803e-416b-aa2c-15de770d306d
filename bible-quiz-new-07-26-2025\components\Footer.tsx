import Link from 'next/link';
import { bibleBooks } from '../data/bible-books';

const footerSections = {
  bibleQuizzes: [
    { name: 'Genesis Quiz', href: '/genesis-quiz/' },
    { name: '<PERSON> Quiz', href: '/matthew-quiz/' },
    { name: '<PERSON> Quiz', href: '/john-quiz/' },
    { name: 'Romans Quiz', href: '/romans-quiz/' },
    { name: 'Psalms Quiz', href: '/psalms-quiz/' },
    { name: 'Revelation Quiz', href: '/revelation-quiz/' },
    { name: 'Jesus Quiz', href: '/jesus-quiz/' },
    { name: '<PERSON> Quiz', href: '/moses-quiz/' },
    { name: '<PERSON> Q<PERSON>', href: '/david-quiz/' },
    { name: '<PERSON> Quiz', href: '/paul-quiz/' },
    { name: 'All Quizzes', href: '/bible-quizzes/' },
    { name: 'Quiz Categories', href: '/bible-quiz-categories/' },
  ],
  studyResources: [
    { name: 'Bible Study Plans', href: '/bible-study-plans/' },
    { name: 'Chapter Summaries', href: '/bible-chapter-summaries/' },
    { name: 'Book Overviews', href: '/bible-book-overviews/' },
    { name: 'Character Studies', href: '/bible-character-studies/' },
    { name: 'Verse Explanations', href: '/bible-verses-explained/' },
    { name: 'Memory Verses', href: '/bible-memory-verses/' },
    { name: 'Discussion Questions', href: '/bible-discussion-questions/' },
    { name: 'Bible Timeline', href: '/bible-timeline/' },
    { name: 'Maps & Charts', href: '/bible-maps-charts/' },
    { name: 'Printable Resources', href: '/printable-bible-resources/' },
    { name: 'Teaching Materials', href: '/bible-teaching-materials/' },
    { name: 'Study Guides', href: '/bible-study-guides/' },
  ],
  popularTopics: [
    { name: 'Ten Commandments', href: '/ten-commandments-quiz/' },
    { name: 'Lord\'s Prayer', href: '/lords-prayer-quiz/' },
    { name: 'Miracles of Jesus', href: '/miracles-jesus-quiz/' },
    { name: 'Parables', href: '/parables-quiz/' },
    { name: 'Christmas', href: '/christmas-bible-quiz/' },
    { name: 'Easter', href: '/easter-bible-quiz/' },
    { name: 'Creation', href: '/creation-quiz/' },
    { name: 'Noah\'s Ark', href: '/noahs-ark-quiz/' },
    { name: 'Exodus', href: '/exodus-quiz/' },
    { name: 'Crucifixion', href: '/crucifixion-quiz/' },
    { name: 'Resurrection', href: '/resurrection-quiz/' },
    { name: 'All Topics', href: '/bible-quiz-topics/' },
  ],
  siteInfo: [
    { name: 'About Us', href: '/about/' },
    { name: 'Contact', href: '/contact/' },
    { name: 'How It Works', href: '/how-bible-quiz-works/' },
    { name: 'FAQ', href: '/bible-quiz-faq/' },
    { name: 'Privacy Policy', href: '/privacy-policy/' },
    { name: 'Terms of Service', href: '/terms-of-service/' },
    { name: 'Sitemap', href: '/sitemap/' },
    { name: 'Newsletter', href: '/newsletter-signup/' },
    { name: 'Submit Quiz', href: '/submit-quiz-questions/' },
    { name: 'Feedback', href: '/feedback/' },
    { name: 'Mobile App', href: '/bible-quiz-app/' },
    { name: 'RSS Feed', href: '/rss/' },
  ],
};

const quizCategories = [
  { name: 'Easy Bible Quiz', href: '/easy-bible-quiz/' },
  { name: 'Hard Bible Quiz', href: '/hard-bible-quiz/' },
  { name: 'Kids Bible Quiz', href: '/kids-bible-quiz/' },
  { name: 'Youth Bible Quiz', href: '/youth-bible-quiz/' },
  { name: 'Adult Bible Quiz', href: '/adult-bible-quiz/' },
  { name: 'Multiple Choice', href: '/multiple-choice-bible-quiz/' },
  { name: 'True/False', href: '/true-false-bible-quiz/' },
  { name: 'Fill in Blank', href: '/fill-in-blank-bible-quiz/' },
  { name: 'Printable Quiz', href: '/printable-bible-quiz/' },
  { name: 'Interactive Quiz', href: '/interactive-bible-quiz/' },
  { name: 'Daily Bible Quiz', href: '/daily-bible-quiz/' },
  { name: 'Quiz Games', href: '/bible-quiz-games/' },
];

export default function Footer() {
  const oldTestamentBooks = bibleBooks.filter(book => book.testament === 'old');
  const newTestamentBooks = bibleBooks.filter(book => book.testament === 'new');

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Bible Quizzes Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-blue-400">Bible Quizzes</h3>
            <ul className="space-y-2">
              {footerSections.bibleQuizzes.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-gray-300 hover:text-white text-sm transition-colors"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Study Resources Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-blue-400">Study Resources</h3>
            <ul className="space-y-2">
              {footerSections.studyResources.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-gray-300 hover:text-white text-sm transition-colors"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Popular Topics Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-blue-400">Popular Topics</h3>
            <ul className="space-y-2">
              {footerSections.popularTopics.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-gray-300 hover:text-white text-sm transition-colors"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Site Info Column */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-blue-400">Site Information</h3>
            <ul className="space-y-2">
              {footerSections.siteInfo.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="text-gray-300 hover:text-white text-sm transition-colors"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Bible Books Section */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Old Testament */}
            <div>
              <h4 className="text-md font-semibold mb-4 text-blue-400">Old Testament Books</h4>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 text-xs">
                {oldTestamentBooks.map((book) => (
                  <Link
                    key={book.id}
                    href={`/${book.slug}-quiz/`}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {book.name}
                  </Link>
                ))}
              </div>
            </div>

            {/* New Testament */}
            <div>
              <h4 className="text-md font-semibold mb-4 text-blue-400">New Testament Books</h4>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 text-xs">
                {newTestamentBooks.map((book) => (
                  <Link
                    key={book.id}
                    href={`/${book.slug}-quiz/`}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {book.name}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quiz Categories Widget */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h4 className="text-md font-semibold mb-4 text-blue-400">Quiz Categories</h4>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
            {quizCategories.map((category) => (
              <Link
                key={category.name}
                href={category.href}
                className="text-gray-400 hover:text-white text-sm transition-colors"
              >
                {category.name}
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm">
              © 2025 SalvationCall | 
              <Link href="/privacy-policy/" className="hover:text-white ml-1">Privacy Policy</Link> | 
              <Link href="/terms/" className="hover:text-white ml-1">Terms</Link> | 
              <Link href="/contact/" className="hover:text-white ml-1">Contact</Link> | 
              <Link href="/sitemap/" className="hover:text-white ml-1">Sitemap</Link>
            </div>
            <div className="flex space-x-4 mt-2 sm:mt-0">
              <span className="text-gray-400 text-sm">Social:</span>
              <Link href="#" className="text-gray-400 hover:text-white text-sm">Facebook</Link>
              <Link href="#" className="text-gray-400 hover:text-white text-sm">Twitter</Link>
              <Link href="#" className="text-gray-400 hover:text-white text-sm">YouTube</Link>
              <Link href="#" className="text-gray-400 hover:text-white text-sm">Instagram</Link>
              <Link href="#" className="text-gray-400 hover:text-white text-sm">Pinterest</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
