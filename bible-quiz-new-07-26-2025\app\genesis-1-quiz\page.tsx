import { Metadata } from 'next';
import QuizPageComponent from '../../components/QuizPageComponent';
import { sampleQuizzes } from '../../data/sample-quizzes';

export const metadata: Metadata = {
  title: 'Genesis 1 Quiz - Test Your Bible Knowledge | SalvationCall',
  description: 'Test your knowledge of Genesis chapter 1 with this interactive Bible quiz. 5 questions covering creation, key verses, and themes with instant results.',
  keywords: ['genesis quiz', 'bible quiz', 'genesis chapter 1', 'creation quiz', 'bible knowledge'],
  openGraph: {
    title: 'Genesis 1 Quiz - Test Your Bible Knowledge | SalvationCall',
    description: 'Test your knowledge of Genesis chapter 1 with this interactive Bible quiz.',
    type: 'article',
    url: 'https://salvationcall.com/genesis-1-quiz/',
    siteName: 'SalvationCall',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Genesis 1 Quiz - Test Your Bible Knowledge | SalvationCall',
    description: 'Test your knowledge of Genesis chapter 1 with this interactive Bible quiz.',
  },
  alternates: {
    canonical: 'https://salvationcall.com/genesis-1-quiz/',
  },
};

export default function Genesis1QuizPage() {
  const quiz = sampleQuizzes.find(q => q.id === 'genesis-1-quiz');
  
  if (!quiz) {
    return <div>Quiz not found</div>;
  }

  return <QuizPageComponent quiz={quiz} />;
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600;
