import { BibleCharacter } from '../types/bible';

export const bibleCharacters: BibleCharacter[] = [
  // Major Old Testament Characters
  {
    id: 'abraham',
    name: '<PERSON>',
    alternativeNames: ['<PERSON>bra<PERSON>'],
    testament: 'old',
    timePeriod: 'Patriarchal Period (c. 2000 BC)',
    keyRole: 'Father of Faith and the Hebrew Nation',
    booksAppearing: ['Genesis', 'Romans', 'Hebrews', '<PERSON>'],
    description: 'Called by God to leave his homeland and become the father of many nations.',
    keyEvents: ['Call from Ur', 'Covenant with God', 'Sacrifice of <PERSON>', 'Purchase of burial site'],
    slug: 'abraham'
  },
  {
    id: 'moses',
    name: '<PERSON>',
    alternativeNames: [],
    testament: 'old',
    timePeriod: 'Exodus Period (c. 1400 BC)',
    keyRole: 'Lawgiver and Deliverer of Israel',
    booksAppearing: ['Exodus', 'Leviticus', 'Numbers', 'Deuteronomy'],
    description: 'Led Israel out of Egypt and received the Law at Mount Sinai.',
    keyEvents: ['Burning Bush', 'Ten Plagues', 'Exodus from Egypt', 'Receiving the Law', 'Wilderness Wandering'],
    slug: 'moses'
  },
  {
    id: 'david',
    name: '<PERSON>',
    alternativeNames: ['King <PERSON>'],
    testament: 'old',
    timePeriod: 'United Kingdom Period (c. 1000 BC)',
    keyRole: 'King of Israel and Man After God\'s Own Heart',
    booksAppearing: ['1 <PERSON>', '2 <PERSON>', '1 Kings', '1 Chronicles', 'Psalms'],
    description: 'Shepherd boy who became Israel\'s greatest king and wrote many psalms.',
    keyEvents: ['Anointing by <PERSON>', 'Defeating Goliath', 'Friendship with Jonathan', 'Becoming King', 'Davidic Covenant'],
    slug: 'david'
  },
  {
    id: 'solomon',
    name: 'Solomon',
    alternativeNames: ['King Solomon'],
    testament: 'old',
    timePeriod: 'United Kingdom Period (c. 970-930 BC)',
    keyRole: 'Wisest King of Israel',
    booksAppearing: ['1 Kings', '2 Chronicles', 'Proverbs', 'Ecclesiastes', 'Song of Solomon'],
    description: 'Known for his wisdom, wealth, and building the first temple.',
    keyEvents: ['Request for Wisdom', 'Building the Temple', 'Visit of Queen of Sheba', 'Decline in Later Years'],
    slug: 'solomon'
  },
  {
    id: 'noah',
    name: 'Noah',
    alternativeNames: [],
    testament: 'old',
    timePeriod: 'Pre-Flood Era',
    keyRole: 'Righteous Man Who Built the Ark',
    booksAppearing: ['Genesis', 'Matthew', 'Luke', 'Hebrews', '1 Peter'],
    description: 'Saved humanity and animals from the great flood.',
    keyEvents: ['Building the Ark', 'The Great Flood', 'God\'s Covenant with Rainbow'],
    slug: 'noah'
  },
  {
    id: 'joseph',
    name: 'Joseph',
    alternativeNames: ['Joseph the Dreamer'],
    testament: 'old',
    timePeriod: 'Patriarchal Period (c. 1900 BC)',
    keyRole: 'Dreamer and Savior of Israel',
    booksAppearing: ['Genesis', 'Exodus', 'Acts', 'Hebrews'],
    description: 'Sold into slavery by his brothers but became second in command in Egypt.',
    keyEvents: ['Coat of Many Colors', 'Sold into Slavery', 'Interpreting Dreams', 'Saving Egypt from Famine'],
    slug: 'joseph'
  },
  {
    id: 'daniel',
    name: 'Daniel',
    alternativeNames: ['Belteshazzar'],
    testament: 'old',
    timePeriod: 'Babylonian Exile (c. 600-530 BC)',
    keyRole: 'Prophet and Interpreter of Dreams',
    booksAppearing: ['Daniel', 'Matthew', 'Mark'],
    description: 'Remained faithful to God while serving in foreign courts.',
    keyEvents: ['Refusing King\'s Food', 'Interpreting Dreams', 'Fiery Furnace', 'Lion\'s Den', 'Prophetic Visions'],
    slug: 'daniel'
  },
  {
    id: 'elijah',
    name: 'Elijah',
    alternativeNames: ['Elias'],
    testament: 'old',
    timePeriod: 'Divided Kingdom (c. 870-850 BC)',
    keyRole: 'Prophet of Fire',
    booksAppearing: ['1 Kings', '2 Kings', 'Matthew', 'Mark', 'Luke', 'James'],
    description: 'Powerful prophet who confronted idolatry and was taken up to heaven.',
    keyEvents: ['Contest on Mount Carmel', 'Fed by Ravens', 'Raising the Widow\'s Son', 'Taken up in Whirlwind'],
    slug: 'elijah'
  },
  {
    id: 'esther',
    name: 'Esther',
    alternativeNames: ['Hadassah'],
    testament: 'old',
    timePeriod: 'Persian Period (c. 480 BC)',
    keyRole: 'Queen Who Saved Her People',
    booksAppearing: ['Esther'],
    description: 'Jewish queen who risked her life to save her people from genocide.',
    keyEvents: ['Becoming Queen', 'Mordecai\'s Discovery', 'Haman\'s Plot', 'Brave Intervention', 'Purim Festival'],
    slug: 'esther'
  },
  {
    id: 'job',
    name: 'Job',
    alternativeNames: [],
    testament: 'old',
    timePeriod: 'Patriarchal Period',
    keyRole: 'Example of Faith Through Suffering',
    booksAppearing: ['Job', 'Ezekiel', 'James'],
    description: 'Righteous man who endured great suffering but maintained his faith.',
    keyEvents: ['Satan\'s Challenge', 'Loss of Family and Wealth', 'Friends\' Counsel', 'God\'s Response', 'Restoration'],
    slug: 'job'
  },

  // Major New Testament Characters
  {
    id: 'jesus',
    name: 'Jesus',
    alternativeNames: ['Jesus Christ', 'Christ', 'Messiah', 'Son of God', 'Son of Man'],
    testament: 'new',
    timePeriod: 'First Century AD (c. 4 BC - 30 AD)',
    keyRole: 'Savior of the World',
    booksAppearing: ['Matthew', 'Mark', 'Luke', 'John', 'Acts', 'All NT Books'],
    description: 'The Son of God who came to earth to save humanity from sin.',
    keyEvents: ['Virgin Birth', 'Baptism', 'Temptation', 'Ministry', 'Crucifixion', 'Resurrection', 'Ascension'],
    slug: 'jesus'
  },
  {
    id: 'paul',
    name: 'Paul',
    alternativeNames: ['Saul', 'Apostle Paul', 'Saul of Tarsus'],
    testament: 'new',
    timePeriod: 'First Century AD (c. 5-67 AD)',
    keyRole: 'Apostle to the Gentiles',
    booksAppearing: ['Acts', 'Romans', '1-2 Corinthians', 'Galatians', 'Ephesians', 'Philippians', 'Colossians', '1-2 Thessalonians', '1-2 Timothy', 'Titus', 'Philemon'],
    description: 'Former persecutor of Christians who became the greatest missionary.',
    keyEvents: ['Persecution of Christians', 'Damascus Road Conversion', 'Three Missionary Journeys', 'Imprisonment', 'Martyrdom'],
    slug: 'paul'
  },
  {
    id: 'peter',
    name: 'Peter',
    alternativeNames: ['Simon', 'Simon Peter', 'Cephas'],
    testament: 'new',
    timePeriod: 'First Century AD',
    keyRole: 'Leader of the Apostles',
    booksAppearing: ['Matthew', 'Mark', 'Luke', 'John', 'Acts', '1 Peter', '2 Peter'],
    description: 'Fisherman who became the spokesman for the twelve apostles.',
    keyEvents: ['Call to Follow Jesus', 'Walking on Water', 'Confession of Faith', 'Denial of Jesus', 'Restoration', 'Pentecost Sermon'],
    slug: 'peter'
  },
  {
    id: 'john',
    name: 'John',
    alternativeNames: ['John the Apostle', 'Beloved Disciple', 'Son of Thunder'],
    testament: 'new',
    timePeriod: 'First Century AD',
    keyRole: 'Apostle of Love',
    booksAppearing: ['Matthew', 'Mark', 'Luke', 'John', 'Acts', '1 John', '2 John', '3 John', 'Revelation'],
    description: 'One of Jesus\' closest disciples, known for his writings on love.',
    keyEvents: ['Call with James', 'Transfiguration', 'At the Cross', 'Empty Tomb', 'Exile to Patmos', 'Revelation Vision'],
    slug: 'john'
  },
  {
    id: 'mary',
    name: 'Mary',
    alternativeNames: ['Mary of Nazareth', 'Virgin Mary', 'Mother of Jesus'],
    testament: 'new',
    timePeriod: 'First Century AD',
    keyRole: 'Mother of Jesus',
    booksAppearing: ['Matthew', 'Mark', 'Luke', 'John', 'Acts'],
    description: 'Young woman chosen by God to be the mother of Jesus.',
    keyEvents: ['Annunciation', 'Visitation to Elizabeth', 'Birth of Jesus', 'Presentation in Temple', 'At the Cross'],
    slug: 'mary'
  },
  {
    id: 'john-baptist',
    name: 'John the Baptist',
    alternativeNames: ['John the Baptizer', 'The Forerunner'],
    testament: 'new',
    timePeriod: 'First Century AD',
    keyRole: 'Forerunner of Christ',
    booksAppearing: ['Matthew', 'Mark', 'Luke', 'John', 'Acts'],
    description: 'Prophet who prepared the way for Jesus\' ministry.',
    keyEvents: ['Birth Announcement', 'Ministry in Wilderness', 'Baptizing Jesus', 'Imprisonment', 'Martyrdom'],
    slug: 'john-baptist'
  }
];

// Helper functions
export function getCharacterBySlug(slug: string): BibleCharacter | undefined {
  return bibleCharacters.find(character => character.slug === slug);
}

export function getCharactersByTestament(testament: 'old' | 'new' | 'both'): BibleCharacter[] {
  return bibleCharacters.filter(character => 
    character.testament === testament || character.testament === 'both'
  );
}

export function getAllCharacterSlugs(): string[] {
  return bibleCharacters.map(character => character.slug);
}

export function searchCharacters(query: string): BibleCharacter[] {
  const lowercaseQuery = query.toLowerCase();
  return bibleCharacters.filter(character =>
    character.name.toLowerCase().includes(lowercaseQuery) ||
    character.alternativeNames.some(name => name.toLowerCase().includes(lowercaseQuery)) ||
    character.description.toLowerCase().includes(lowercaseQuery)
  );
}
