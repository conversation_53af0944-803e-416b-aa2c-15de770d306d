const { chromium } = require('playwright');

async function debugPage() {
  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();
  
  try {
    console.log('Navigating to http://localhost:3000/genesis-quiz/...');
    await page.goto('http://localhost:3000/genesis-quiz/', { waitUntil: 'networkidle' });
    
    console.log('Page loaded. Getting title...');
    const title = await page.title();
    console.log('Page title:', title);
    
    console.log('Checking for specific elements...');
    
    // Check if it's showing the listing page
    const listingHeader = await page.$('h1:has-text("Bible Quiz Genesis 1-50")');
    if (listingHeader) {
      console.log('✅ LISTING PAGE DETECTED: Found "Bible Quiz Genesis 1-50" header');
    } else {
      console.log('❌ LISTING PAGE NOT FOUND');
    }
    
    // Check if it's showing the original quiz page
    const originalHeader = await page.$('h1:has-text("Genesis Quiz")');
    if (originalHeader) {
      const headerText = await originalHeader.textContent();
      console.log('❌ ORIGINAL QUIZ PAGE DETECTED:', headerText);
    }
    
    // Get all h1 elements to see what's actually on the page
    const h1Elements = await page.$$eval('h1', elements => 
      elements.map(el => el.textContent.trim())
    );
    console.log('All H1 elements found:', h1Elements);
    
    // Check for console errors
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        logs.push(`Console Error: ${msg.text()}`);
      }
    });
    
    // Wait a bit for any console errors
    await page.waitForTimeout(2000);
    
    if (logs.length > 0) {
      console.log('Console errors found:');
      logs.forEach(log => console.log(log));
    } else {
      console.log('No console errors found');
    }
    
    // Get the page content to see what's actually rendered
    const bodyText = await page.$eval('body', el => el.textContent.substring(0, 500));
    console.log('First 500 chars of body text:', bodyText);

    // Take a screenshot to see what's actually displayed
    console.log('Taking screenshot...');
    await page.screenshot({ path: 'genesis-quiz-debug.png', fullPage: true });
    console.log('Screenshot saved as genesis-quiz-debug.png');

    // Get the full HTML to see what's being rendered
    const html = await page.content();
    console.log('Page HTML length:', html.length);

    // Check if we can find specific elements that should be on the listing page
    const breadcrumbs = await page.$('nav.bg-white.border-b');
    console.log('Breadcrumbs found:', !!breadcrumbs);

    const quizGrid = await page.$('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.gap-6');
    console.log('Quiz grid found:', !!quizGrid);

    const articleCards = await page.$$('.bg-white.rounded-lg.shadow-sm');
    console.log('Article cards found:', articleCards.length);

    // Check for the specific article elements
    const articles = await page.$$('article');
    console.log('Article elements found:', articles.length);

    // Check for any div with space-y-6 class (the container)
    const container = await page.$('.space-y-6');
    console.log('Container with space-y-6 found:', !!container);

    // Check if allQuizzes array is populated by looking for any quiz links
    const quizLinks = await page.$$('a[href*="quiz"]');
    console.log('Quiz links found:', quizLinks.length);

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await browser.close();
  }
}

debugPage();
