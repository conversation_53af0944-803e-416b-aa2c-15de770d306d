// Core Bible data types and interfaces

export interface BibleBook {
  id: string;
  name: string;
  fullName: string;
  testament: 'old' | 'new';
  chapters: number;
  category: string;
  description: string;
  keyThemes: string[];
  slug: string;
}

export interface BibleChapter {
  bookId: string;
  chapter: number;
  verses: number;
  keyEvents: string[];
  keyCharacters: string[];
  summary: string;
  slug: string;
}

export interface BibleCharacter {
  id: string;
  name: string;
  alternativeNames: string[];
  testament: 'old' | 'new' | 'both';
  timePeriod: string;
  keyRole: string;
  booksAppearing: string[];
  description: string;
  keyEvents: string[];
  slug: string;
}

export interface QuizTheme {
  id: string;
  name: string;
  description: string;
  category: string;
  relatedBooks: string[];
  relatedCharacters: string[];
  keyAspects: string[];
  slug: string;
}

export interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number; // Index of correct option (0-3)
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  reference?: string; // Bible verse reference
  category: string;
}

export interface Quiz {
  id: string;
  title: string;
  description: string;
  type: 'chapter' | 'book' | 'character' | 'theme' | 'difficulty';
  difficulty: 'easy' | 'medium' | 'hard';
  questions: QuizQuestion[];
  estimatedTime: number; // in minutes
  relatedQuizzes: string[];
  seoTitle: string;
  seoDescription: string;
  keywords: string[];
  slug: string;
  bookId?: string;
  chapter?: number;
  characterId?: string;
  themeId?: string;
}

export interface QuizResult {
  score: number;
  totalQuestions: number;
  percentage: number;
  timeSpent: number;
  correctAnswers: number[];
  incorrectAnswers: number[];
  performanceMessage: string;
}

export interface QuizStats {
  questionCount: number;
  estimatedTime: number;
  difficulty: string;
  completedCount: number;
}

// SEO and Schema types
export interface QuizSEO {
  title: string;
  description: string;
  keywords: string[];
  canonicalUrl: string;
  ogTitle: string;
  ogDescription: string;
  ogImage: string;
  structuredData: any;
}

export interface BreadcrumbItem {
  name: string;
  url: string;
}

// Navigation and UI types
export interface NavigationItem {
  name: string;
  href: string;
  children?: NavigationItem[];
}

export interface QuizCard {
  id: string;
  title: string;
  description: string;
  difficulty: string;
  questionCount: number;
  estimatedTime: number;
  completedCount: number;
  slug: string;
  image?: string;
}

// Filter and search types
export interface QuizFilters {
  testament?: 'old' | 'new';
  difficulty?: 'easy' | 'medium' | 'hard';
  type?: 'chapter' | 'book' | 'character' | 'theme';
  category?: string;
  searchTerm?: string;
}

export interface SearchResult {
  id: string;
  title: string;
  description: string;
  type: string;
  url: string;
  relevanceScore: number;
}
