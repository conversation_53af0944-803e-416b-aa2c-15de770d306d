import { bibleBooks } from '../data/bible-books';
import { bibleCharacters } from '../data/bible-characters';
import { quizThemes } from '../data/quiz-themes';
import { Quiz, BibleBook, BibleCharacter, QuizTheme } from '../types/bible';

// Generate all possible quiz slugs for static generation
export function generateAllQuizSlugs(): string[] {
  const slugs: string[] = [];
  
  // Chapter-based quizzes (1,189 total)
  bibleBooks.forEach(book => {
    for (let chapter = 1; chapter <= book.chapters; chapter++) {
      slugs.push(`${book.slug}-${chapter}-quiz`);
    }
  });
  
  // Book-level quizzes (66 total)
  bibleBooks.forEach(book => {
    slugs.push(`${book.slug}-quiz`);
  });
  
  // Character quizzes
  bibleCharacters.forEach(character => {
    slugs.push(`${character.slug}-quiz`);
  });
  
  // Theme quizzes
  quizThemes.forEach(theme => {
    slugs.push(theme.slug);
  });
  
  return slugs;
}

// Generate quiz metadata for a given slug
export function generateQuizMetadata(slug: string): Quiz | null {
  // Check if it's a chapter quiz
  const chapterMatch = slug.match(/^(.+)-(\d+)-quiz$/);
  if (chapterMatch) {
    const [, bookSlug, chapterStr] = chapterMatch;
    const chapter = parseInt(chapterStr);
    const book = bibleBooks.find(b => b.slug === bookSlug);
    
    if (book && chapter <= book.chapters) {
      return generateChapterQuiz(book, chapter);
    }
  }
  
  // Check if it's a book quiz
  const bookMatch = slug.match(/^(.+)-quiz$/);
  if (bookMatch) {
    const [, bookSlug] = bookMatch;
    const book = bibleBooks.find(b => b.slug === bookSlug);
    
    if (book) {
      return generateBookQuiz(book);
    }
  }
  
  // Check if it's a character quiz
  const characterMatch = slug.match(/^(.+)-quiz$/);
  if (characterMatch) {
    const [, characterSlug] = characterMatch;
    const character = bibleCharacters.find(c => c.slug === characterSlug);
    
    if (character) {
      return generateCharacterQuiz(character);
    }
  }
  
  // Check if it's a theme quiz
  const theme = quizThemes.find(t => t.slug === slug);
  if (theme) {
    return generateThemeQuiz(theme);
  }
  
  return null;
}

// Generate chapter quiz metadata
function generateChapterQuiz(book: BibleBook, chapter: number): Quiz {
  const title = `${book.name} ${chapter} Quiz`;
  const slug = `${book.slug}-${chapter}-quiz`;
  
  return {
    id: slug,
    title,
    description: `Test your knowledge of ${book.name} chapter ${chapter} with this comprehensive Bible quiz.`,
    type: 'chapter',
    difficulty: 'medium',
    questions: [], // Questions would be loaded separately
    estimatedTime: 5,
    relatedQuizzes: generateRelatedQuizzes(book, chapter),
    seoTitle: `${title} - Test Your Bible Knowledge | SalvationCall`,
    seoDescription: `Test your knowledge of ${book.name} chapter ${chapter} with this interactive Bible quiz. Multiple choice questions covering key verses, characters, and themes with instant results.`,
    keywords: [
      `${book.slug} quiz`,
      'bible quiz',
      `${book.slug} chapter ${chapter}`,
      'scripture test',
      'bible knowledge'
    ],
    slug,
    bookId: book.id,
    chapter
  };
}

// Generate book quiz metadata
function generateBookQuiz(book: BibleBook): Quiz {
  const title = `${book.name} Quiz`;
  const slug = `${book.slug}-quiz`;
  
  return {
    id: slug,
    title,
    description: `Comprehensive quiz covering the entire book of ${book.name}. Test your knowledge of key events, characters, and themes.`,
    type: 'book',
    difficulty: 'hard',
    questions: [], // Questions would be loaded separately
    estimatedTime: 15,
    relatedQuizzes: generateBookRelatedQuizzes(book),
    seoTitle: `${title} - Complete Bible Book Quiz | SalvationCall`,
    seoDescription: `Test your knowledge of the book of ${book.name} with this comprehensive Bible quiz. Covering all ${book.chapters} chapters with detailed questions and explanations.`,
    keywords: [
      `${book.slug} quiz`,
      'bible quiz',
      `book of ${book.slug}`,
      `${book.testament} testament`,
      'bible knowledge'
    ],
    slug,
    bookId: book.id
  };
}

// Generate character quiz metadata
function generateCharacterQuiz(character: BibleCharacter): Quiz {
  const title = `${character.name} Bible Quiz`;
  const slug = `${character.slug}-quiz`;
  
  return {
    id: slug,
    title,
    description: `Test your knowledge about ${character.name}, ${character.keyRole}. This quiz covers ${character.name}'s life story, key events, and spiritual lessons.`,
    type: 'character',
    difficulty: 'medium',
    questions: [], // Questions would be loaded separately
    estimatedTime: 8,
    relatedQuizzes: generateCharacterRelatedQuizzes(character),
    seoTitle: `${title} - Test Your Knowledge | SalvationCall`,
    seoDescription: `Challenge yourself with this ${character.name} Bible quiz! Questions about ${character.keyRole.toLowerCase()}, key biblical moments, and spiritual lessons. Test your knowledge now!`,
    keywords: [
      `${character.slug} quiz`,
      'bible quiz',
      `${character.name} bible`,
      'bible characters',
      'bible knowledge'
    ],
    slug,
    characterId: character.id
  };
}

// Generate theme quiz metadata
function generateThemeQuiz(theme: QuizTheme): Quiz {
  const title = `${theme.name} Bible Quiz`;
  
  return {
    id: theme.slug,
    title,
    description: theme.description,
    type: 'theme',
    difficulty: 'medium',
    questions: [], // Questions would be loaded separately
    estimatedTime: 10,
    relatedQuizzes: generateThemeRelatedQuizzes(theme),
    seoTitle: `${title} - Scripture Knowledge Test | SalvationCall`,
    seoDescription: `Explore ${theme.name.toLowerCase()} in Scripture with this comprehensive Bible quiz. Questions from Old and New Testament passages. Perfect for Bible study groups!`,
    keywords: [
      theme.slug.replace('-quiz', ''),
      'bible quiz',
      theme.category.toLowerCase(),
      'scripture test',
      'bible knowledge'
    ],
    slug: theme.slug,
    themeId: theme.id
  };
}

// Helper functions for related quizzes
function generateRelatedQuizzes(book: BibleBook, chapter: number): string[] {
  const related: string[] = [];
  
  // Previous and next chapters
  if (chapter > 1) {
    related.push(`${book.slug}-${chapter - 1}-quiz`);
  }
  if (chapter < book.chapters) {
    related.push(`${book.slug}-${chapter + 1}-quiz`);
  }
  
  // Book quiz
  related.push(`${book.slug}-quiz`);
  
  return related;
}

function generateBookRelatedQuizzes(book: BibleBook): string[] {
  const related: string[] = [];
  
  // First few chapters
  for (let i = 1; i <= Math.min(3, book.chapters); i++) {
    related.push(`${book.slug}-${i}-quiz`);
  }
  
  // Other books in same category
  const sameCategory = bibleBooks.filter(b => 
    b.category === book.category && b.id !== book.id
  ).slice(0, 3);
  
  sameCategory.forEach(b => related.push(`${b.slug}-quiz`));
  
  return related;
}

function generateCharacterRelatedQuizzes(character: BibleCharacter): string[] {
  const related: string[] = [];
  
  // Books where character appears
  character.booksAppearing.slice(0, 3).forEach(bookName => {
    const book = bibleBooks.find(b => b.name === bookName);
    if (book) {
      related.push(`${book.slug}-quiz`);
    }
  });
  
  return related;
}

function generateThemeRelatedQuizzes(theme: QuizTheme): string[] {
  const related: string[] = [];
  
  // Related books
  theme.relatedBooks.slice(0, 3).forEach(bookName => {
    const book = bibleBooks.find(b => b.name === bookName);
    if (book) {
      related.push(`${book.slug}-quiz`);
    }
  });
  
  // Related characters
  theme.relatedCharacters.slice(0, 2).forEach(characterName => {
    const character = bibleCharacters.find(c => c.name === characterName);
    if (character) {
      related.push(`${character.slug}-quiz`);
    }
  });
  
  return related;
}

// Generate breadcrumbs for SEO
export function generateBreadcrumbs(quiz: Quiz): Array<{name: string, url: string}> {
  const breadcrumbs = [
    { name: 'Home', url: '/' },
    { name: 'Bible Quizzes', url: '/bible-quizzes/' }
  ];
  
  if (quiz.type === 'chapter' && quiz.bookId) {
    const book = bibleBooks.find(b => b.id === quiz.bookId);
    if (book) {
      breadcrumbs.push({
        name: `${book.name} Quizzes`,
        url: `/bible-quizzes/${book.slug}/`
      });
    }
  }
  
  breadcrumbs.push({
    name: quiz.title,
    url: `/${quiz.slug}/`
  });
  
  return breadcrumbs;
}
