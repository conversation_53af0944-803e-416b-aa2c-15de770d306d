import { BibleBook } from '../types/bible';

export const bibleBooks: BibleBook[] = [
  // Old Testament
  {
    id: 'genesis',
    name: '<PERSON>',
    fullName: 'The Book of Genesis',
    testament: 'old',
    chapters: 50,
    category: 'Law',
    description: 'The book of beginnings, covering creation, the fall, and the patriarchs.',
    keyThemes: ['Creation', 'Fall', 'Covenant', 'Patriarchs', 'Promise'],
    slug: 'genesis'
  },
  {
    id: 'exodus',
    name: 'Exodus',
    fullName: 'The Book of Exodus',
    testament: 'old',
    chapters: 40,
    category: 'Law',
    description: 'The deliverance of Israel from Egypt and the giving of the Law.',
    keyThemes: ['Deliverance', 'Law', 'Covenant', 'Worship', 'Leadership'],
    slug: 'exodus'
  },
  {
    id: 'leviticus',
    name: 'Levi<PERSON>',
    fullName: 'The Book of Leviticus',
    testament: 'old',
    chapters: 27,
    category: 'Law',
    description: 'Laws for worship, sacrifice, and holy living.',
    keyThemes: ['Holiness', 'Sacrifice', 'Priesthood', 'Purity', 'Worship'],
    slug: 'leviticus'
  },
  {
    id: 'numbers',
    name: 'Numbers',
    fullName: 'The Book of Numbers',
    testament: 'old',
    chapters: 36,
    category: 'Law',
    description: 'Israel\'s wilderness wanderings and preparation for the Promised Land.',
    keyThemes: ['Wilderness', 'Faith', 'Rebellion', 'Journey', 'Preparation'],
    slug: 'numbers'
  },
  {
    id: 'deuteronomy',
    name: 'Deuteronomy',
    fullName: 'The Book of Deuteronomy',
    testament: 'old',
    chapters: 34,
    category: 'Law',
    description: 'Moses\' final speeches and the renewal of the covenant.',
    keyThemes: ['Covenant', 'Obedience', 'Love', 'Blessing', 'Choice'],
    slug: 'deuteronomy'
  },
  {
    id: 'joshua',
    name: 'Joshua',
    fullName: 'The Book of Joshua',
    testament: 'old',
    chapters: 24,
    category: 'History',
    description: 'The conquest and settlement of the Promised Land.',
    keyThemes: ['Conquest', 'Faith', 'Victory', 'Inheritance', 'Leadership'],
    slug: 'joshua'
  },
  {
    id: 'judges',
    name: 'Judges',
    fullName: 'The Book of Judges',
    testament: 'old',
    chapters: 21,
    category: 'History',
    description: 'The cycle of sin, oppression, and deliverance in early Israel.',
    keyThemes: ['Cycle', 'Deliverance', 'Leadership', 'Apostasy', 'Mercy'],
    slug: 'judges'
  },
  {
    id: 'ruth',
    name: 'Ruth',
    fullName: 'The Book of Ruth',
    testament: 'old',
    chapters: 4,
    category: 'History',
    description: 'A story of loyalty, love, and redemption.',
    keyThemes: ['Loyalty', 'Redemption', 'Love', 'Providence', 'Faithfulness'],
    slug: 'ruth'
  },
  {
    id: '1-samuel',
    name: '1 Samuel',
    fullName: 'The First Book of Samuel',
    testament: 'old',
    chapters: 31,
    category: 'History',
    description: 'The transition from judges to kings, featuring Samuel, Saul, and David.',
    keyThemes: ['Leadership', 'Kingship', 'Obedience', 'Heart', 'Anointing'],
    slug: '1-samuel'
  },
  {
    id: '2-samuel',
    name: '2 Samuel',
    fullName: 'The Second Book of Samuel',
    testament: 'old',
    chapters: 24,
    category: 'History',
    description: 'David\'s reign as king of Israel.',
    keyThemes: ['Kingship', 'Covenant', 'Sin', 'Forgiveness', 'Dynasty'],
    slug: '2-samuel'
  },
  {
    id: '1-kings',
    name: '1 Kings',
    fullName: 'The First Book of Kings',
    testament: 'old',
    chapters: 22,
    category: 'History',
    description: 'Solomon\'s reign and the division of the kingdom.',
    keyThemes: ['Wisdom', 'Temple', 'Division', 'Apostasy', 'Prophecy'],
    slug: '1-kings'
  },
  {
    id: '2-kings',
    name: '2 Kings',
    fullName: 'The Second Book of Kings',
    testament: 'old',
    chapters: 25,
    category: 'History',
    description: 'The decline and fall of Israel and Judah.',
    keyThemes: ['Decline', 'Exile', 'Prophecy', 'Judgment', 'Remnant'],
    slug: '2-kings'
  },
  {
    id: '1-chronicles',
    name: '1 Chronicles',
    fullName: 'The First Book of Chronicles',
    testament: 'old',
    chapters: 29,
    category: 'History',
    description: 'Genealogies and David\'s reign from a priestly perspective.',
    keyThemes: ['Genealogy', 'Worship', 'Temple', 'Priesthood', 'Heritage'],
    slug: '1-chronicles'
  },
  {
    id: '2-chronicles',
    name: '2 Chronicles',
    fullName: 'The Second Book of Chronicles',
    testament: 'old',
    chapters: 36,
    category: 'History',
    description: 'The history of Judah from Solomon to the exile.',
    keyThemes: ['Temple', 'Reform', 'Revival', 'Judgment', 'Hope'],
    slug: '2-chronicles'
  },
  {
    id: 'ezra',
    name: 'Ezra',
    fullName: 'The Book of Ezra',
    testament: 'old',
    chapters: 10,
    category: 'History',
    description: 'The return from exile and rebuilding of the temple.',
    keyThemes: ['Return', 'Rebuilding', 'Reform', 'Scripture', 'Separation'],
    slug: 'ezra'
  },
  {
    id: 'nehemiah',
    name: 'Nehemiah',
    fullName: 'The Book of Nehemiah',
    testament: 'old',
    chapters: 13,
    category: 'History',
    description: 'Rebuilding Jerusalem\'s walls and spiritual renewal.',
    keyThemes: ['Rebuilding', 'Leadership', 'Prayer', 'Perseverance', 'Reform'],
    slug: 'nehemiah'
  },
  {
    id: 'esther',
    name: 'Esther',
    fullName: 'The Book of Esther',
    testament: 'old',
    chapters: 10,
    category: 'History',
    description: 'God\'s providence in preserving His people.',
    keyThemes: ['Providence', 'Courage', 'Deliverance', 'Identity', 'Timing'],
    slug: 'esther'
  },
  {
    id: 'job',
    name: 'Job',
    fullName: 'The Book of Job',
    testament: 'old',
    chapters: 42,
    category: 'Wisdom',
    description: 'The problem of suffering and God\'s sovereignty.',
    keyThemes: ['Suffering', 'Sovereignty', 'Faith', 'Wisdom', 'Restoration'],
    slug: 'job'
  },
  {
    id: 'psalms',
    name: 'Psalms',
    fullName: 'The Book of Psalms',
    testament: 'old',
    chapters: 150,
    category: 'Wisdom',
    description: 'Songs of worship, praise, and prayer.',
    keyThemes: ['Worship', 'Praise', 'Prayer', 'Trust', 'Deliverance'],
    slug: 'psalms'
  },
  {
    id: 'proverbs',
    name: 'Proverbs',
    fullName: 'The Book of Proverbs',
    testament: 'old',
    chapters: 31,
    category: 'Wisdom',
    description: 'Practical wisdom for daily living.',
    keyThemes: ['Wisdom', 'Character', 'Relationships', 'Work', 'Speech'],
    slug: 'proverbs'
  },
  {
    id: 'ecclesiastes',
    name: 'Ecclesiastes',
    fullName: 'The Book of Ecclesiastes',
    testament: 'old',
    chapters: 12,
    category: 'Wisdom',
    description: 'The search for meaning and purpose in life.',
    keyThemes: ['Meaning', 'Vanity', 'Time', 'Wisdom', 'Fear of God'],
    slug: 'ecclesiastes'
  },
  {
    id: 'song-of-solomon',
    name: 'Song of Solomon',
    fullName: 'The Song of Solomon',
    testament: 'old',
    chapters: 8,
    category: 'Wisdom',
    description: 'A celebration of love and marriage.',
    keyThemes: ['Love', 'Marriage', 'Beauty', 'Devotion', 'Romance'],
    slug: 'song-of-solomon'
  },
  {
    id: 'isaiah',
    name: 'Isaiah',
    fullName: 'The Book of Isaiah',
    testament: 'old',
    chapters: 66,
    category: 'Major Prophets',
    description: 'Prophecies of judgment and salvation, including Messianic prophecies.',
    keyThemes: ['Holiness', 'Salvation', 'Messiah', 'Judgment', 'Comfort'],
    slug: 'isaiah'
  },
  {
    id: 'jeremiah',
    name: 'Jeremiah',
    fullName: 'The Book of Jeremiah',
    testament: 'old',
    chapters: 52,
    category: 'Major Prophets',
    description: 'The weeping prophet\'s call to repentance and warnings of judgment.',
    keyThemes: ['Judgment', 'Repentance', 'New Covenant', 'Restoration', 'Faithfulness'],
    slug: 'jeremiah'
  },
  {
    id: 'lamentations',
    name: 'Lamentations',
    fullName: 'The Book of Lamentations',
    testament: 'old',
    chapters: 5,
    category: 'Major Prophets',
    description: 'Mourning over Jerusalem\'s destruction.',
    keyThemes: ['Grief', 'Judgment', 'Hope', 'Mercy', 'Restoration'],
    slug: 'lamentations'
  },
  {
    id: 'ezekiel',
    name: 'Ezekiel',
    fullName: 'The Book of Ezekiel',
    testament: 'old',
    chapters: 48,
    category: 'Major Prophets',
    description: 'Visions of God\'s glory and prophecies of restoration.',
    keyThemes: ['Glory', 'Restoration', 'Responsibility', 'New Heart', 'Temple'],
    slug: 'ezekiel'
  },
  {
    id: 'daniel',
    name: 'Daniel',
    fullName: 'The Book of Daniel',
    testament: 'old',
    chapters: 12,
    category: 'Major Prophets',
    description: 'Stories of faith and apocalyptic visions.',
    keyThemes: ['Faith', 'Sovereignty', 'Prophecy', 'Kingdom', 'Perseverance'],
    slug: 'daniel'
  },
  // Minor Prophets
  {
    id: 'hosea',
    name: 'Hosea',
    fullName: 'The Book of Hosea',
    testament: 'old',
    chapters: 14,
    category: 'Minor Prophets',
    description: 'God\'s unfailing love despite Israel\'s unfaithfulness.',
    keyThemes: ['Love', 'Faithfulness', 'Adultery', 'Restoration', 'Mercy'],
    slug: 'hosea'
  },
  {
    id: 'joel',
    name: 'Joel',
    fullName: 'The Book of Joel',
    testament: 'old',
    chapters: 3,
    category: 'Minor Prophets',
    description: 'The Day of the Lord and the outpouring of the Spirit.',
    keyThemes: ['Day of the Lord', 'Repentance', 'Spirit', 'Restoration', 'Judgment'],
    slug: 'joel'
  },
  {
    id: 'amos',
    name: 'Amos',
    fullName: 'The Book of Amos',
    testament: 'old',
    chapters: 9,
    category: 'Minor Prophets',
    description: 'A call for justice and righteousness.',
    keyThemes: ['Justice', 'Righteousness', 'Judgment', 'Social Issues', 'Restoration'],
    slug: 'amos'
  },
  {
    id: 'obadiah',
    name: 'Obadiah',
    fullName: 'The Book of Obadiah',
    testament: 'old',
    chapters: 1,
    category: 'Minor Prophets',
    description: 'Judgment against Edom and restoration of Israel.',
    keyThemes: ['Judgment', 'Pride', 'Justice', 'Restoration', 'Kingdom'],
    slug: 'obadiah'
  },
  {
    id: 'jonah',
    name: 'Jonah',
    fullName: 'The Book of Jonah',
    testament: 'old',
    chapters: 4,
    category: 'Minor Prophets',
    description: 'God\'s mercy extends to all nations.',
    keyThemes: ['Mercy', 'Obedience', 'Missions', 'Repentance', 'Compassion'],
    slug: 'jonah'
  },
  {
    id: 'micah',
    name: 'Micah',
    fullName: 'The Book of Micah',
    testament: 'old',
    chapters: 7,
    category: 'Minor Prophets',
    description: 'Justice, mercy, and humility before God.',
    keyThemes: ['Justice', 'Mercy', 'Humility', 'Messiah', 'Restoration'],
    slug: 'micah'
  },
  {
    id: 'nahum',
    name: 'Nahum',
    fullName: 'The Book of Nahum',
    testament: 'old',
    chapters: 3,
    category: 'Minor Prophets',
    description: 'The fall of Nineveh and God\'s justice.',
    keyThemes: ['Justice', 'Judgment', 'Comfort', 'Sovereignty', 'Vengeance'],
    slug: 'nahum'
  },
  {
    id: 'habakkuk',
    name: 'Habakkuk',
    fullName: 'The Book of Habakkuk',
    testament: 'old',
    chapters: 3,
    category: 'Minor Prophets',
    description: 'Wrestling with God\'s justice and living by faith.',
    keyThemes: ['Faith', 'Justice', 'Trust', 'Sovereignty', 'Prayer'],
    slug: 'habakkuk'
  },
  {
    id: 'zephaniah',
    name: 'Zephaniah',
    fullName: 'The Book of Zephaniah',
    testament: 'old',
    chapters: 3,
    category: 'Minor Prophets',
    description: 'The Day of the Lord and restoration.',
    keyThemes: ['Day of the Lord', 'Judgment', 'Restoration', 'Remnant', 'Joy'],
    slug: 'zephaniah'
  },
  {
    id: 'haggai',
    name: 'Haggai',
    fullName: 'The Book of Haggai',
    testament: 'old',
    chapters: 2,
    category: 'Minor Prophets',
    description: 'Rebuilding the temple and God\'s priorities.',
    keyThemes: ['Priorities', 'Temple', 'Blessing', 'Glory', 'Future'],
    slug: 'haggai'
  },
  {
    id: 'zechariah',
    name: 'Zechariah',
    fullName: 'The Book of Zechariah',
    testament: 'old',
    chapters: 14,
    category: 'Minor Prophets',
    description: 'Visions of restoration and the coming Messiah.',
    keyThemes: ['Restoration', 'Messiah', 'Temple', 'Kingdom', 'Glory'],
    slug: 'zechariah'
  },
  {
    id: 'malachi',
    name: 'Malachi',
    fullName: 'The Book of Malachi',
    testament: 'old',
    chapters: 4,
    category: 'Minor Prophets',
    description: 'The last Old Testament prophet calls for faithfulness.',
    keyThemes: ['Faithfulness', 'Worship', 'Tithing', 'Messenger', 'Day of the Lord'],
    slug: 'malachi'
  },

  // New Testament
  {
    id: 'matthew',
    name: 'Matthew',
    fullName: 'The Gospel According to Matthew',
    testament: 'new',
    chapters: 28,
    category: 'Gospels',
    description: 'Jesus as the promised Messiah and King.',
    keyThemes: ['Messiah', 'Kingdom', 'Fulfillment', 'Teaching', 'Commission'],
    slug: 'matthew'
  },
  {
    id: 'mark',
    name: 'Mark',
    fullName: 'The Gospel According to Mark',
    testament: 'new',
    chapters: 16,
    category: 'Gospels',
    description: 'Jesus as the suffering Servant.',
    keyThemes: ['Service', 'Action', 'Suffering', 'Discipleship', 'Cross'],
    slug: 'mark'
  },
  {
    id: 'luke',
    name: 'Luke',
    fullName: 'The Gospel According to Luke',
    testament: 'new',
    chapters: 24,
    category: 'Gospels',
    description: 'Jesus as the perfect Son of Man.',
    keyThemes: ['Compassion', 'Prayer', 'Holy Spirit', 'Salvation', 'Joy'],
    slug: 'luke'
  },
  {
    id: 'john',
    name: 'John',
    fullName: 'The Gospel According to John',
    testament: 'new',
    chapters: 21,
    category: 'Gospels',
    description: 'Jesus as the Son of God.',
    keyThemes: ['Deity', 'Eternal Life', 'Love', 'Light', 'Truth'],
    slug: 'john'
  },
  {
    id: 'acts',
    name: 'Acts',
    fullName: 'The Acts of the Apostles',
    testament: 'new',
    chapters: 28,
    category: 'History',
    description: 'The birth and growth of the early church.',
    keyThemes: ['Holy Spirit', 'Mission', 'Church', 'Witness', 'Growth'],
    slug: 'acts'
  },
  {
    id: 'romans',
    name: 'Romans',
    fullName: 'The Letter to the Romans',
    testament: 'new',
    chapters: 16,
    category: 'Pauline Epistles',
    description: 'The gospel of God\'s righteousness.',
    keyThemes: ['Righteousness', 'Faith', 'Grace', 'Salvation', 'Sanctification'],
    slug: 'romans'
  },
  {
    id: '1-corinthians',
    name: '1 Corinthians',
    fullName: 'The First Letter to the Corinthians',
    testament: 'new',
    chapters: 16,
    category: 'Pauline Epistles',
    description: 'Addressing problems in the Corinthian church.',
    keyThemes: ['Unity', 'Wisdom', 'Love', 'Spiritual Gifts', 'Resurrection'],
    slug: '1-corinthians'
  },
  {
    id: '2-corinthians',
    name: '2 Corinthians',
    fullName: 'The Second Letter to the Corinthians',
    testament: 'new',
    chapters: 13,
    category: 'Pauline Epistles',
    description: 'Paul\'s defense of his ministry and apostleship.',
    keyThemes: ['Ministry', 'Suffering', 'Comfort', 'Giving', 'Weakness'],
    slug: '2-corinthians'
  },
  {
    id: 'galatians',
    name: 'Galatians',
    fullName: 'The Letter to the Galatians',
    testament: 'new',
    chapters: 6,
    category: 'Pauline Epistles',
    description: 'Freedom from the law through faith in Christ.',
    keyThemes: ['Freedom', 'Faith', 'Grace', 'Spirit', 'Gospel'],
    slug: 'galatians'
  },
  {
    id: 'ephesians',
    name: 'Ephesians',
    fullName: 'The Letter to the Ephesians',
    testament: 'new',
    chapters: 6,
    category: 'Pauline Epistles',
    description: 'Our position and practice in Christ.',
    keyThemes: ['Unity', 'Church', 'Spiritual Blessings', 'Armor of God', 'Love'],
    slug: 'ephesians'
  },
  {
    id: 'philippians',
    name: 'Philippians',
    fullName: 'The Letter to the Philippians',
    testament: 'new',
    chapters: 4,
    category: 'Pauline Epistles',
    description: 'Joy and contentment in Christ.',
    keyThemes: ['Joy', 'Partnership', 'Humility', 'Contentment', 'Peace'],
    slug: 'philippians'
  },
  {
    id: 'colossians',
    name: 'Colossians',
    fullName: 'The Letter to the Colossians',
    testament: 'new',
    chapters: 4,
    category: 'Pauline Epistles',
    description: 'The supremacy and sufficiency of Christ.',
    keyThemes: ['Supremacy', 'Fullness', 'New Life', 'Wisdom', 'Christ'],
    slug: 'colossians'
  },
  {
    id: '1-thessalonians',
    name: '1 Thessalonians',
    fullName: 'The First Letter to the Thessalonians',
    testament: 'new',
    chapters: 5,
    category: 'Pauline Epistles',
    description: 'Encouragement and instruction about Christ\'s return.',
    keyThemes: ['Second Coming', 'Hope', 'Holiness', 'Work', 'Encouragement'],
    slug: '1-thessalonians'
  },
  {
    id: '2-thessalonians',
    name: '2 Thessalonians',
    fullName: 'The Second Letter to the Thessalonians',
    testament: 'new',
    chapters: 3,
    category: 'Pauline Epistles',
    description: 'Clarification about the Day of the Lord.',
    keyThemes: ['Day of the Lord', 'Perseverance', 'Work', 'Prayer', 'Comfort'],
    slug: '2-thessalonians'
  },
  {
    id: '1-timothy',
    name: '1 Timothy',
    fullName: 'The First Letter to Timothy',
    testament: 'new',
    chapters: 6,
    category: 'Pastoral Epistles',
    description: 'Instructions for church leadership and order.',
    keyThemes: ['Leadership', 'Church Order', 'Sound Doctrine', 'Godliness', 'Ministry'],
    slug: '1-timothy'
  },
  {
    id: '2-timothy',
    name: '2 Timothy',
    fullName: 'The Second Letter to Timothy',
    testament: 'new',
    chapters: 4,
    category: 'Pastoral Epistles',
    description: 'Paul\'s final charge to Timothy.',
    keyThemes: ['Faithfulness', 'Endurance', 'Scripture', 'Ministry', 'Legacy'],
    slug: '2-timothy'
  },
  {
    id: 'titus',
    name: 'Titus',
    fullName: 'The Letter to Titus',
    testament: 'new',
    chapters: 3,
    category: 'Pastoral Epistles',
    description: 'Organizing the church in Crete.',
    keyThemes: ['Church Organization', 'Good Works', 'Sound Doctrine', 'Grace', 'Leadership'],
    slug: 'titus'
  },
  {
    id: 'philemon',
    name: 'Philemon',
    fullName: 'The Letter to Philemon',
    testament: 'new',
    chapters: 1,
    category: 'Pauline Epistles',
    description: 'A personal appeal for forgiveness and reconciliation.',
    keyThemes: ['Forgiveness', 'Reconciliation', 'Love', 'Brotherhood', 'Grace'],
    slug: 'philemon'
  },
  {
    id: 'hebrews',
    name: 'Hebrews',
    fullName: 'The Letter to the Hebrews',
    testament: 'new',
    chapters: 13,
    category: 'General Epistles',
    description: 'The superiority of Christ and the new covenant.',
    keyThemes: ['Superiority', 'Faith', 'Priesthood', 'Covenant', 'Perseverance'],
    slug: 'hebrews'
  },
  {
    id: 'james',
    name: 'James',
    fullName: 'The Letter of James',
    testament: 'new',
    chapters: 5,
    category: 'General Epistles',
    description: 'Practical Christian living and faith in action.',
    keyThemes: ['Faith and Works', 'Wisdom', 'Trials', 'Speech', 'Prayer'],
    slug: 'james'
  },
  {
    id: '1-peter',
    name: '1 Peter',
    fullName: 'The First Letter of Peter',
    testament: 'new',
    chapters: 5,
    category: 'General Epistles',
    description: 'Hope and endurance in suffering.',
    keyThemes: ['Suffering', 'Hope', 'Holiness', 'Submission', 'Glory'],
    slug: '1-peter'
  },
  {
    id: '2-peter',
    name: '2 Peter',
    fullName: 'The Second Letter of Peter',
    testament: 'new',
    chapters: 3,
    category: 'General Epistles',
    description: 'Warning against false teachers and the promise of Christ\'s return.',
    keyThemes: ['False Teachers', 'Knowledge', 'Second Coming', 'Growth', 'Scripture'],
    slug: '2-peter'
  },
  {
    id: '1-john',
    name: '1 John',
    fullName: 'The First Letter of John',
    testament: 'new',
    chapters: 5,
    category: 'General Epistles',
    description: 'Assurance of salvation and the nature of love.',
    keyThemes: ['Love', 'Light', 'Life', 'Assurance', 'Fellowship'],
    slug: '1-john'
  },
  {
    id: '2-john',
    name: '2 John',
    fullName: 'The Second Letter of John',
    testament: 'new',
    chapters: 1,
    category: 'General Epistles',
    description: 'Walking in truth and love.',
    keyThemes: ['Truth', 'Love', 'Commandments', 'Deceivers', 'Fellowship'],
    slug: '2-john'
  },
  {
    id: '3-john',
    name: '3 John',
    fullName: 'The Third Letter of John',
    testament: 'new',
    chapters: 1,
    category: 'General Epistles',
    description: 'Hospitality and church leadership.',
    keyThemes: ['Hospitality', 'Truth', 'Leadership', 'Good and Evil', 'Fellowship'],
    slug: '3-john'
  },
  {
    id: 'jude',
    name: 'Jude',
    fullName: 'The Letter of Jude',
    testament: 'new',
    chapters: 1,
    category: 'General Epistles',
    description: 'Contending for the faith against false teachers.',
    keyThemes: ['Contending', 'False Teachers', 'Judgment', 'Faith', 'Mercy'],
    slug: 'jude'
  },
  {
    id: 'revelation',
    name: 'Revelation',
    fullName: 'The Revelation to John',
    testament: 'new',
    chapters: 22,
    category: 'Prophecy',
    description: 'The ultimate victory of Christ and the new creation.',
    keyThemes: ['Victory', 'Judgment', 'New Creation', 'Worship', 'Hope'],
    slug: 'revelation'
  }
];

// Helper functions
export function getBookBySlug(slug: string): BibleBook | undefined {
  return bibleBooks.find(book => book.slug === slug);
}

export function getBooksByTestament(testament: 'old' | 'new'): BibleBook[] {
  return bibleBooks.filter(book => book.testament === testament);
}

export function getBooksByCategory(category: string): BibleBook[] {
  return bibleBooks.filter(book => book.category === category);
}

export function getAllBookSlugs(): string[] {
  return bibleBooks.map(book => book.slug);
}
