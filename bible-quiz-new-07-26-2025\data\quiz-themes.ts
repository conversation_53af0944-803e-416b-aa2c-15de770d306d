import { QuizTheme } from '../types/bible';

export const quizThemes: QuizTheme[] = [
  {
    id: 'miracles-of-jesus',
    name: 'Miracles of Jesus',
    description: 'Explore the supernatural works of <PERSON> that demonstrated His divine power and compassion.',
    category: 'Jesus\' Ministry',
    relatedBooks: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    relatedCharacters: ['<PERSON>'],
    keyAspects: [
      'Healing the sick and disabled',
      'Raising the dead',
      'Nature miracles',
      'Feeding the multitudes',
      'Casting out demons'
    ],
    slug: 'miracles-of-jesus-quiz'
  },
  {
    id: 'parables',
    name: 'Parables of <PERSON>',
    description: 'Test your knowledge of <PERSON>\' teaching stories that reveal kingdom truths.',
    category: '<PERSON>\' Teaching',
    relatedBooks: ['<PERSON>', '<PERSON>', '<PERSON>'],
    relatedCharacters: ['<PERSON>'],
    keyAspects: [
      'Kingdom of Heaven parables',
      'Lost and found stories',
      'Stewardship parables',
      'End times parables',
      'Character and conduct stories'
    ],
    slug: 'parables-quiz'
  },
  {
    id: 'ten-commandments',
    name: 'Ten Commandments',
    description: 'The fundamental moral laws given by God to <PERSON> at Mount Sinai.',
    category: 'Law and Commandments',
    relatedBooks: ['Exodus', 'Deuteronomy'],
    relatedCharacters: ['Moses'],
    keyAspects: [
      'Worship of God alone',
      'No idolatry',
      'Reverence for God\'s name',
      'Sabbath observance',
      'Honor parents',
      'Sanctity of life',
      'Marriage fidelity',
      'Honesty in possessions',
      'Truthfulness',
      'Contentment'
    ],
    slug: 'ten-commandments-quiz'
  },
  {
    id: 'fruits-of-spirit',
    name: 'Fruits of the Spirit',
    description: 'The character qualities that the Holy Spirit produces in believers.',
    category: 'Christian Living',
    relatedBooks: ['Galatians', 'Romans', 'Ephesians'],
    relatedCharacters: ['Paul'],
    keyAspects: [
      'Love - unconditional care',
      'Joy - deep happiness',
      'Peace - inner tranquility',
      'Patience - endurance',
      'Kindness - gentle goodness',
      'Goodness - moral excellence',
      'Faithfulness - reliability',
      'Gentleness - humility',
      'Self-control - discipline'
    ],
    slug: 'fruits-of-spirit-quiz'
  },
  {
    id: 'armor-of-god',
    name: 'Armor of God',
    description: 'The spiritual protection and weapons God provides for spiritual warfare.',
    category: 'Spiritual Warfare',
    relatedBooks: ['Ephesians'],
    relatedCharacters: ['Paul'],
    keyAspects: [
      'Belt of truth',
      'Breastplate of righteousness',
      'Feet shod with peace',
      'Shield of faith',
      'Helmet of salvation',
      'Sword of the Spirit'
    ],
    slug: 'armor-of-god-quiz'
  },
  {
    id: 'biblical-prophecy',
    name: 'Biblical Prophecy',
    description: 'Prophecies about the Messiah, end times, and God\'s plan for humanity.',
    category: 'Prophecy',
    relatedBooks: ['Isaiah', 'Jeremiah', 'Ezekiel', 'Daniel', 'Revelation'],
    relatedCharacters: ['Isaiah', 'Jeremiah', 'Ezekiel', 'Daniel', 'John'],
    keyAspects: [
      'Messianic prophecies',
      'End times events',
      'Judgment and restoration',
      'Second coming of Christ',
      'New heaven and earth'
    ],
    slug: 'biblical-prophecy-quiz'
  },
  {
    id: 'prayer-in-bible',
    name: 'Prayer in the Bible',
    description: 'Examples and teachings about prayer throughout Scripture.',
    category: 'Spiritual Disciplines',
    relatedBooks: ['Psalms', 'Matthew', 'Luke', '1 Thessalonians'],
    relatedCharacters: ['David', 'Jesus', 'Paul'],
    keyAspects: [
      'Lord\'s Prayer',
      'Prayers of Jesus',
      'Psalms as prayers',
      'Paul\'s prayer life',
      'Answered prayers',
      'Persistent prayer'
    ],
    slug: 'prayer-in-bible-quiz'
  },
  {
    id: 'love-in-scripture',
    name: 'Love in Scripture',
    description: 'God\'s love for humanity and how we should love others.',
    category: 'Christian Character',
    relatedBooks: ['1 Corinthians', '1 John', 'John', 'Romans'],
    relatedCharacters: ['Jesus', 'John', 'Paul'],
    keyAspects: [
      'God\'s love for us',
      'Love chapter (1 Cor 13)',
      'Greatest commandments',
      'Love for enemies',
      'Sacrificial love',
      'Love in action'
    ],
    slug: 'love-in-scripture-quiz'
  },
  {
    id: 'creation-story',
    name: 'Creation Story',
    description: 'The biblical account of how God created the universe and humanity.',
    category: 'Origins',
    relatedBooks: ['Genesis', 'Psalms', 'Job'],
    relatedCharacters: ['Adam', 'Eve'],
    keyAspects: [
      'Six days of creation',
      'Creation of humanity',
      'Garden of Eden',
      'Image of God',
      'Sabbath rest',
      'Stewardship mandate'
    ],
    slug: 'creation-quiz'
  },
  {
    id: 'christmas-story',
    name: 'Christmas Story',
    description: 'The birth of Jesus Christ and surrounding events.',
    category: 'Life of Christ',
    relatedBooks: ['Matthew', 'Luke'],
    relatedCharacters: ['Jesus', 'Mary', 'Joseph', 'Angels', 'Shepherds', 'Wise Men'],
    keyAspects: [
      'Annunciation to Mary',
      'Journey to Bethlehem',
      'Birth in a manger',
      'Angels and shepherds',
      'Visit of the wise men',
      'Flight to Egypt'
    ],
    slug: 'christmas-bible-quiz'
  },
  {
    id: 'easter-story',
    name: 'Easter Story',
    description: 'The crucifixion and resurrection of Jesus Christ.',
    category: 'Life of Christ',
    relatedBooks: ['Matthew', 'Mark', 'Luke', 'John'],
    relatedCharacters: ['Jesus', 'Pilate', 'Mary Magdalene', 'Disciples'],
    keyAspects: [
      'Last Supper',
      'Garden of Gethsemane',
      'Trial and crucifixion',
      'Death and burial',
      'Empty tomb',
      'Resurrection appearances'
    ],
    slug: 'easter-bible-quiz'
  },
  {
    id: 'women-in-bible',
    name: 'Women in the Bible',
    description: 'Remarkable women who played important roles in biblical history.',
    category: 'Biblical Characters',
    relatedBooks: ['Genesis', 'Exodus', 'Judges', 'Ruth', 'Esther', 'Luke'],
    relatedCharacters: ['Eve', 'Sarah', 'Rebekah', 'Rachel', 'Miriam', 'Deborah', 'Ruth', 'Esther', 'Mary'],
    keyAspects: [
      'Matriarchs of faith',
      'Prophetesses and judges',
      'Queens and leaders',
      'Mothers and wives',
      'Examples of courage',
      'Models of faithfulness'
    ],
    slug: 'women-bible-characters'
  }
];

// Helper functions
export function getThemeBySlug(slug: string): QuizTheme | undefined {
  return quizThemes.find(theme => theme.slug === slug);
}

export function getThemesByCategory(category: string): QuizTheme[] {
  return quizThemes.filter(theme => theme.category === category);
}

export function getAllThemeSlugs(): string[] {
  return quizThemes.map(theme => theme.slug);
}

export function searchThemes(query: string): QuizTheme[] {
  const lowercaseQuery = query.toLowerCase();
  return quizThemes.filter(theme =>
    theme.name.toLowerCase().includes(lowercaseQuery) ||
    theme.description.toLowerCase().includes(lowercaseQuery) ||
    theme.keyAspects.some(aspect => aspect.toLowerCase().includes(lowercaseQuery))
  );
}

export function getThemeCategories(): string[] {
  const categories = new Set(quizThemes.map(theme => theme.category));
  return Array.from(categories);
}
