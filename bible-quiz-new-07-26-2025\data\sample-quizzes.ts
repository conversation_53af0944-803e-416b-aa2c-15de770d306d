import { Quiz, QuizQuestion } from '../types/bible';
import { genesisQuizzes, generateAllGenesisQuizzes } from './genesis-quizzes';

// Sample questions for Genesis 1
const genesis1Questions: QuizQuestion[] = [
  {
    id: 'gen1-q1',
    question: 'What did God create on the first day?',
    options: ['Light and darkness', 'Heaven and earth', 'Sun and moon', 'Plants and trees'],
    correctAnswer: 0,
    explanation: 'God said "Let there be light" and separated light from darkness on the first day.',
    difficulty: 'easy',
    reference: 'Genesis 1:3-5',
    category: 'creation'
  },
  {
    id: 'gen1-q2',
    question: 'On which day did God create the sun, moon, and stars?',
    options: ['Day 1', 'Day 2', 'Day 3', 'Day 4'],
    correctAnswer: 3,
    explanation: 'God created the greater light (sun), lesser light (moon), and stars on the fourth day.',
    difficulty: 'easy',
    reference: 'Genesis 1:14-19',
    category: 'creation'
  },
  {
    id: 'gen1-q3',
    question: 'What did God create on the sixth day?',
    options: ['Fish and birds', 'Land animals and humans', 'Plants and trees', 'Heaven and earth'],
    correctAnswer: 1,
    explanation: 'On the sixth day, God created land animals and then created man in His own image.',
    difficulty: 'easy',
    reference: 'Genesis 1:24-31',
    category: 'creation'
  },
  {
    id: 'gen1-q4',
    question: 'In whose image did God create mankind?',
    options: ['Angels', 'Animals', 'His own image', 'No specific image mentioned'],
    correctAnswer: 2,
    explanation: 'God created mankind in His own image, both male and female.',
    difficulty: 'easy',
    reference: 'Genesis 1:27',
    category: 'creation'
  },
  {
    id: 'gen1-q5',
    question: 'What did God do on the seventh day?',
    options: ['Created more animals', 'Rested from His work', 'Created the garden', 'Made the first covenant'],
    correctAnswer: 1,
    explanation: 'God rested on the seventh day from all His work and blessed and sanctified it.',
    difficulty: 'easy',
    reference: 'Genesis 2:2-3',
    category: 'creation'
  }
];

// Sample questions for John 3
const john3Questions: QuizQuestion[] = [
  {
    id: 'john3-q1',
    question: 'Who came to Jesus by night in John 3?',
    options: ['Peter', 'Nicodemus', 'Thomas', 'Andrew'],
    correctAnswer: 1,
    explanation: 'Nicodemus, a Pharisee and ruler of the Jews, came to Jesus by night.',
    difficulty: 'easy',
    reference: 'John 3:1-2',
    category: 'jesus-ministry'
  },
  {
    id: 'john3-q2',
    question: 'What did Jesus say one must do to see the kingdom of God?',
    options: ['Be baptized', 'Be born again', 'Follow the law', 'Give to the poor'],
    correctAnswer: 1,
    explanation: 'Jesus told Nicodemus that unless one is born again, he cannot see the kingdom of God.',
    difficulty: 'medium',
    reference: 'John 3:3',
    category: 'salvation'
  },
  {
    id: 'john3-q3',
    question: 'Complete this famous verse: "For God so loved the world that He gave His..."',
    options: ['only Son', 'greatest gift', 'holy word', 'divine blessing'],
    correctAnswer: 0,
    explanation: 'John 3:16 says God gave His only begotten Son so that whoever believes should not perish.',
    difficulty: 'easy',
    reference: 'John 3:16',
    category: 'salvation'
  },
  {
    id: 'john3-q4',
    question: 'According to John 3:16, what must one do to have eternal life?',
    options: ['Be baptized', 'Follow the commandments', 'Believe in Him', 'Do good works'],
    correctAnswer: 2,
    explanation: 'Whoever believes in Him should not perish but have everlasting life.',
    difficulty: 'easy',
    reference: 'John 3:16',
    category: 'salvation'
  },
  {
    id: 'john3-q5',
    question: 'What comparison did Jesus use to explain being born again?',
    options: ['Wind blowing', 'Seed growing', 'Water flowing', 'Fire burning'],
    correctAnswer: 0,
    explanation: 'Jesus compared the Spirit to wind - you hear its sound but don\'t know where it comes from or goes.',
    difficulty: 'medium',
    reference: 'John 3:8',
    category: 'holy-spirit'
  }
];

// Sample quiz data - now includes comprehensive Genesis quizzes
export const sampleQuizzes: Quiz[] = [
  // Add all Genesis quizzes
  ...Object.values(genesisQuizzes),
  ...generateAllGenesisQuizzes().filter(quiz => !Object.values(genesisQuizzes).some(existing => existing.id === quiz.id)),

  // Keep existing John 3 quiz
  {
    id: 'john-3-quiz',
    title: 'John 3 Quiz',
    description: 'Test your knowledge of John chapter 3, including the famous conversation with Nicodemus.',
    type: 'chapter',
    difficulty: 'medium',
    questions: john3Questions,
    estimatedTime: 5,
    relatedQuizzes: ['john-1-quiz', 'salvation-quiz', 'jesus-quiz'],
    seoTitle: 'John 3 Quiz - Test Your Bible Knowledge | SalvationCall',
    seoDescription: 'Test your knowledge of John chapter 3 with this interactive Bible quiz. 5 questions covering Nicodemus, being born again, and John 3:16.',
    keywords: ['john quiz', 'bible quiz', 'john chapter 3', 'nicodemus', 'born again', 'john 3:16'],
    slug: 'john-3-quiz',
    bookId: 'john',
    chapter: 3
  }
];

// Helper functions
export function getQuizBySlug(slug: string): Quiz | undefined {
  // First check Genesis quizzes for better performance
  const genesisQuiz = Object.values(genesisQuizzes).find(quiz => quiz.slug === slug);
  if (genesisQuiz) return genesisQuiz;

  // Then check all sample quizzes
  return sampleQuizzes.find(quiz => quiz.slug === slug);
}

export function getQuizzesByBook(bookId: string): Quiz[] {
  return sampleQuizzes.filter(quiz => quiz.bookId === bookId);
}

export function getQuizzesByDifficulty(difficulty: 'easy' | 'medium' | 'hard'): Quiz[] {
  return sampleQuizzes.filter(quiz => quiz.difficulty === difficulty);
}

export function getQuizzesByType(type: 'chapter' | 'book' | 'character' | 'theme'): Quiz[] {
  return sampleQuizzes.filter(quiz => quiz.type === type);
}

// Get all available quizzes (including generated ones)
export function getAllQuizzes(): Quiz[] {
  return sampleQuizzes;
}

// Get quiz questions by quiz ID
export function getQuizQuestions(quizId: string): QuizQuestion[] {
  const quiz = sampleQuizzes.find(q => q.id === quizId);
  return quiz?.questions || [];
}
